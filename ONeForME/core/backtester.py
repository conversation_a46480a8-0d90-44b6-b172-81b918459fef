import pandas as pd
from datetime import datetime, timedelta
from core.strategy_manager import StrategyManager
from core.exchange_manager import ExchangeManager
from config.settings import settings
from loguru import logger

class Backtester:
    def __init__(self, strategy_manager: StrategyManager, exchange_manager: ExchangeManager):
        self.strategy_manager = strategy_manager
        self.exchange_manager = exchange_manager
        self.start_date = datetime.now() - timedelta(days=30)
        self.end_date = datetime.now()
        
    async def backtest_strategy(self, strategy: str, exchange: str, symbol: str):
        """Voert een backtest uit voor een specifieke strategie"""
        try:
            # Historische data ophalen
            data = await self.exchange_manager.fetch_ohlcv(exchange, symbol, 
                                                         timeframe='1h', 
                                                         since=self.start_date.timestamp(),
                                                         until=self.end_date.timestamp())
            if not data:
                logger.error("Geen historische data gevonden")
                return
                
            df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Strategie toepassen
            strategy_instance = self.strategy_manager.strategies[strategy]
            buy_signals = []
            sell_signals = []
            
            for i in range(len(df)):
                if i > 0:
                    if await strategy_instance.should_enter(exchange, symbol):
                        buy_signals.append(df.iloc[i]['close'])
                        sell_signals.append(None)
                    elif await strategy_instance.should_exit(exchange, symbol):
                        sell_signals.append(df.iloc[i]['close'])
                        buy_signals.append(None)
                    else:
                        buy_signals.append(None)
                        sell_signals.append(None)
                else:
                    buy_signals.append(None)
                    sell_signals.append(None)
            
            df['buy_signal'] = buy_signals
            df['sell_signal'] = sell_signals
            
            # Resultaten analyseren
            df['position'] = df['buy_signal'].ffill().fillna(0) - df['sell_signal'].ffill().fillna(0)
            df['pnl'] = df['close'].pct_change() * df['position']
            
            total_pnl = df['pnl'].cumsum().iloc[-1]
            max_drawdown = (df['close'].cummax() - df['close']) / df['close'].cummax()
            sharpe_ratio = df['pnl'].mean() / df['pnl'].std() * (252 ** 0.5)
            
            logger.info(f"Backtest resultaten:\n"
                       f"Totale PnL: {total_pnl:.2%}\n"
                       f"Maximale drawdown: {max_drawdown.max():.2%}\n"
                       f"Sharpe Ratio: {sharpe_ratio:.2f}")
            
        except Exception as e:
            logger.error(f"Fout tijdens backtest: {e}")
