from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import field_validator, Field
from loguru import logger
import os
from typing import List


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    # Environment Configuration
    ENVIRONMENT: str = Field(
        default="development",
        description="Environment: development, staging, production",
    )
    DEBUG: bool = Field(default=True, description="Enable debug mode")

    # Telegram Configuration
    TELEGRAM_BOT_TOKEN: str = Field(default="", description="Telegram bot token")
    ADMIN_USER_IDS: str = Field(
        default="", description="Comma-separated admin user IDs"
    )
    VIEWER_USER_IDS: str = Field(
        default="", description="Comma-separated viewer user IDs"
    )
    MONITORING_CHANNEL: str = Field(default="", description="Monitoring channel ID")

    # Exchange API Keys
    KUCOIN_API_KEY: str = Field(default="", description="KuCoin API key")
    KUCOIN_SECRET: str = Field(default="", description="KuCoin secret")
    KUCOIN_PASSWORD: str = Field(default="", description="KuCoin password")
    BINANCE_API_KEY: str = Field(default="", description="Binance API key")
    BINANCE_SECRET: str = Field(default="", description="Binance secret")
    BYBIT_API_KEY: str = Field(default="", description="Bybit API key")
    BYBIT_SECRET: str = Field(default="", description="Bybit secret")

    # Trading Configuration
    TRADING_PAIRS: str = Field(
        default="BTC/USDT,ETH/USDT", description="Comma-separated trading pairs"
    )
    TIMEFRAME: str = Field(default="5m", description="Trading timeframe")
    PAPER_TRADING: bool = Field(default=True, description="Enable paper trading mode")

    # Risk Management - KRITIEKE INSTELLINGEN VOOR LIVE TRADING
    RISK_PERCENT_PER_TRADE: float = Field(
        default=1.0, ge=0.1, le=5.0, description="Risk percentage per trade (0.1-5.0%)"
    )
    MAX_CONCURRENT_POSITIONS: int = Field(
        default=2, ge=1, le=5, description="Maximum concurrent positions (1-5)"
    )
    MAX_DRAWDOWN_PERCENT: float = Field(
        default=5.0, ge=1.0, le=15.0, description="Maximum drawdown percentage (1-15%)"
    )
    POSITION_SIZE_TYPE: str = Field(
        default="percentage", description="Position size type"
    )

    # Nieuwe veiligheidsinstellingen
    MAX_DAILY_TRADES: int = Field(
        default=10, ge=1, le=50, description="Maximum trades per day"
    )
    MIN_ACCOUNT_BALANCE: float = Field(
        default=100.0,
        ge=10.0,
        description="Minimum account balance to continue trading",
    )
    EMERGENCY_STOP: bool = Field(
        default=False, description="Emergency stop all trading"
    )
    LIVE_TRADING_ENABLED: bool = Field(
        default=False,
        description="Enable live trading (requires explicit confirmation)",
    )

    # Stop Loss en Take Profit
    DEFAULT_STOP_LOSS_PERCENT: float = Field(
        default=2.0, ge=0.5, le=10.0, description="Default stop loss percentage"
    )
    DEFAULT_TAKE_PROFIT_PERCENT: float = Field(
        default=3.0, ge=1.0, le=20.0, description="Default take profit percentage"
    )

    # Strategy Parameters
    RSI_PERIOD: int = 14
    MACD_FAST: int = 12
    MACD_SLOW: int = 26
    MACD_SIGNAL: int = 9

    # Database
    DATABASE_URL: str = "sqlite+aiosqlite:///./trading_bot.db"

    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/trading_bot.log"

    @field_validator("TELEGRAM_BOT_TOKEN")
    @classmethod
    def validate_bot_token(cls, v):
        if v and not v.startswith(("1", "2", "5", "6", "7")):
            raise ValueError("Invalid Telegram bot token format")
        return v

    @field_validator("PAPER_TRADING", "LIVE_TRADING_ENABLED")
    @classmethod
    def validate_trading_mode(cls, v, info):
        # Als live trading enabled is, moet paper trading disabled zijn
        if info.field_name == "LIVE_TRADING_ENABLED" and v:
            logger.warning("🚨 LIVE TRADING ENABLED - GEBRUIK ECHT GELD! 🚨")
        return v

    def is_live_trading_safe(self) -> tuple[bool, list[str]]:
        """Controleer of live trading veilig kan worden gestart"""
        errors = []

        if not self.TELEGRAM_BOT_TOKEN:
            errors.append("Telegram bot token is niet ingesteld")

        if not any([self.BINANCE_API_KEY, self.KUCOIN_API_KEY]):
            errors.append("Geen exchange API keys ingesteld")

        if self.RISK_PERCENT_PER_TRADE > 2.0:
            errors.append(
                f"Risk per trade te hoog: {self.RISK_PERCENT_PER_TRADE}% (max 2%)"
            )

        if self.MAX_CONCURRENT_POSITIONS > 3:
            errors.append(
                f"Te veel concurrent positions: {self.MAX_CONCURRENT_POSITIONS} (max 3)"
            )

        if self.EMERGENCY_STOP:
            errors.append("Emergency stop is geactiveerd")

        if not self.LIVE_TRADING_ENABLED:
            errors.append("Live trading is niet expliciet enabled")

        return len(errors) == 0, errors

    @property
    def trading_pairs_list(self) -> list[str]:
        """Converteer comma-separated trading pairs naar lijst"""
        return [pair.strip() for pair in self.TRADING_PAIRS.split(",") if pair.strip()]

    @property
    def admin_user_ids_list(self) -> list[int]:
        """Converteer comma-separated admin IDs naar lijst"""
        if not self.ADMIN_USER_IDS:
            return []
        return [
            int(uid.strip())
            for uid in self.ADMIN_USER_IDS.split(",")
            if uid.strip().isdigit()
        ]


# Instantiate settings
settings = Settings()

# Logging configuration
logger.remove(0)  # Remove default handler
log_file = os.path.join(os.path.dirname(__file__), "../logs/trading_bot.log")
logger.add(log_file, rotation="10 MB", compression="zip", level="INFO")
logger.add(lambda msg: print(msg, end=""), level="DEBUG")  # Console output
