import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from telegram.ext import Application

from core.exchange_manager import ExchangeManager
from core.strategy_manager import StrategyManager
from core.risk_manager import RiskManager


class TestMainFunctionality:
    """Test suite voor main.py functionaliteit"""

    @pytest.mark.asyncio
    async def test_managers_initialization(self):
        """Test of alle managers correct worden geïnitialiseerd"""
        with patch('main.ExchangeManager') as mock_em, \
             patch('main.StrategyManager') as mock_sm, \
             patch('main.RiskManager') as mock_rm:
            
            # Mock de constructors
            mock_exchange_manager = MagicMock()
            mock_strategy_manager = MagicMock()
            mock_risk_manager = MagicMock()
            
            mock_em.return_value = mock_exchange_manager
            mock_sm.return_value = mock_strategy_manager
            mock_rm.return_value = mock_risk_manager
            
            # Mock close_exchanges method
            mock_exchange_manager.close_exchanges = AsyncMock()
            
            # Import en test main functie
            from main import main
            
            # Mock ApplicationBuilder en Application
            with patch('main.ApplicationBuilder') as mock_app_builder:
                mock_application = MagicMock()
                mock_application.run_polling = MagicMock(side_effect=KeyboardInterrupt())
                mock_app_builder.return_value.token.return_value.build.return_value = mock_application
                
                # Mock set_managers
                with patch('main.set_managers') as mock_set_managers:
                    # Test main functie
                    await main()
                    
                    # Controleer of managers zijn aangemaakt
                    mock_em.assert_called_once()
                    mock_sm.assert_called_once_with(mock_exchange_manager)
                    mock_rm.assert_called_once_with(mock_exchange_manager)
                    
                    # Controleer of set_managers is aangeroepen
                    mock_set_managers.assert_called_once_with(
                        mock_exchange_manager, 
                        mock_strategy_manager, 
                        mock_risk_manager
                    )
                    
                    # Controleer of close_exchanges is aangeroepen
                    mock_exchange_manager.close_exchanges.assert_called_once()

    @pytest.mark.asyncio
    async def test_application_builder_configuration(self):
        """Test of de Telegram Application correct wordt geconfigureerd"""
        with patch('main.ExchangeManager'), \
             patch('main.StrategyManager'), \
             patch('main.RiskManager'), \
             patch('main.set_managers'):
            
            from main import main
            
            # Mock ApplicationBuilder
            with patch('main.ApplicationBuilder') as mock_app_builder:
                mock_application = MagicMock()
                mock_application.run_polling = MagicMock(side_effect=KeyboardInterrupt())
                
                # Mock de builder chain
                mock_builder = MagicMock()
                mock_builder.token.return_value.build.return_value = mock_application
                mock_app_builder.return_value = mock_builder
                
                # Mock exchange manager close method
                with patch('main.ExchangeManager') as mock_em:
                    mock_exchange_manager = MagicMock()
                    mock_exchange_manager.close_exchanges = AsyncMock()
                    mock_em.return_value = mock_exchange_manager
                    
                    await main()
                    
                    # Controleer of ApplicationBuilder correct is gebruikt
                    mock_app_builder.assert_called_once()
                    mock_builder.token.assert_called_once()
                    
                    # Controleer of handlers zijn toegevoegd
                    assert mock_application.add_handler.call_count >= 5  # start, status, help, monitoring, unknown
                    mock_application.add_error_handler.assert_called_once()

    @pytest.mark.asyncio
    async def test_handlers_registration(self):
        """Test of alle handlers correct worden geregistreerd"""
        with patch('main.ExchangeManager'), \
             patch('main.StrategyManager'), \
             patch('main.RiskManager'), \
             patch('main.set_managers'):
            
            from main import main
            
            # Mock ApplicationBuilder
            with patch('main.ApplicationBuilder') as mock_app_builder:
                mock_application = MagicMock()
                mock_application.run_polling = MagicMock(side_effect=KeyboardInterrupt())
                mock_app_builder.return_value.token.return_value.build.return_value = mock_application
                
                # Mock exchange manager
                with patch('main.ExchangeManager') as mock_em:
                    mock_exchange_manager = MagicMock()
                    mock_exchange_manager.close_exchanges = AsyncMock()
                    mock_em.return_value = mock_exchange_manager
                    
                    await main()
                    
                    # Controleer welke handlers zijn geregistreerd
                    handler_calls = mock_application.add_handler.call_args_list
                    
                    # We verwachten 5 handlers: start, status, help, monitoring, unknown
                    assert len(handler_calls) == 5
                    
                    # Controleer error handler
                    mock_application.add_error_handler.assert_called_once()

    @pytest.mark.asyncio
    async def test_keyboard_interrupt_handling(self):
        """Test of KeyboardInterrupt correct wordt afgehandeld"""
        with patch('main.ExchangeManager') as mock_em, \
             patch('main.StrategyManager'), \
             patch('main.RiskManager'), \
             patch('main.set_managers'):
            
            # Mock exchange manager
            mock_exchange_manager = MagicMock()
            mock_exchange_manager.close_exchanges = AsyncMock()
            mock_em.return_value = mock_exchange_manager
            
            from main import main
            
            # Mock ApplicationBuilder
            with patch('main.ApplicationBuilder') as mock_app_builder:
                mock_application = MagicMock()
                mock_application.run_polling = MagicMock(side_effect=KeyboardInterrupt())
                mock_app_builder.return_value.token.return_value.build.return_value = mock_application
                
                # Test dat main functie geen exception gooit bij KeyboardInterrupt
                await main()
                
                # Controleer of close_exchanges nog steeds wordt aangeroepen
                mock_exchange_manager.close_exchanges.assert_called_once()

    @pytest.mark.asyncio
    async def test_general_exception_handling(self):
        """Test of algemene exceptions correct worden afgehandeld"""
        with patch('main.ExchangeManager') as mock_em, \
             patch('main.StrategyManager'), \
             patch('main.RiskManager'), \
             patch('main.set_managers'):
            
            # Mock exchange manager
            mock_exchange_manager = MagicMock()
            mock_exchange_manager.close_exchanges = AsyncMock()
            mock_em.return_value = mock_exchange_manager
            
            from main import main
            
            # Mock ApplicationBuilder om een exception te gooien
            with patch('main.ApplicationBuilder') as mock_app_builder:
                mock_application = MagicMock()
                mock_application.run_polling = MagicMock(side_effect=Exception("Test error"))
                mock_app_builder.return_value.token.return_value.build.return_value = mock_application
                
                # Test dat main functie geen exception gooit
                await main()
                
                # Controleer of close_exchanges nog steeds wordt aangeroepen
                mock_exchange_manager.close_exchanges.assert_called_once()

    @pytest.mark.asyncio
    async def test_close_exchanges_exception_handling(self):
        """Test of exceptions bij close_exchanges correct worden afgehandeld"""
        with patch('main.ExchangeManager') as mock_em, \
             patch('main.StrategyManager'), \
             patch('main.RiskManager'), \
             patch('main.set_managers'):
            
            # Mock exchange manager om exception te gooien bij close
            mock_exchange_manager = MagicMock()
            mock_exchange_manager.close_exchanges = AsyncMock(side_effect=Exception("Close error"))
            mock_em.return_value = mock_exchange_manager
            
            from main import main
            
            # Mock ApplicationBuilder
            with patch('main.ApplicationBuilder') as mock_app_builder:
                mock_application = MagicMock()
                mock_application.run_polling = MagicMock(side_effect=KeyboardInterrupt())
                mock_app_builder.return_value.token.return_value.build.return_value = mock_application
                
                # Test dat main functie geen exception gooit, zelfs als close_exchanges faalt
                await main()
                
                # Controleer of close_exchanges is aangeroepen (ook al faalde het)
                mock_exchange_manager.close_exchanges.assert_called_once()

    def test_main_entry_point(self):
        """Test of het main entry point correct werkt"""
        # Test dat we main.py kunnen importeren zonder errors
        import main
        
        # Test dat de main functie bestaat
        assert hasattr(main, 'main')
        assert callable(main.main)
        
        # Test dat asyncio.run wordt aangeroepen in __main__ block
        # Dit is moeilijk te testen zonder de code uit te voeren,
        # maar we kunnen controleren of de structuur correct is
        assert hasattr(main, '__name__')
