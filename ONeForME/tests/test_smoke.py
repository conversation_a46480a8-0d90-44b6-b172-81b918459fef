import pytest
from core.exchange_manager import ExchangeManager
from core.strategy_manager import StrategyManager
from core.risk_manager import RiskManager
from config.settings import settings

@pytest.mark.asyncio
async def test_exchange_initialization():
    """Test of exchanges correct worden geïnitialiseerd"""
    exchange_manager = ExchangeManager()
    await exchange_manager.initialize_exchanges()
    assert len(exchange_manager.exchanges) > 0

@pytest.mark.asyncio
async def test_strategy_initialization():
    """Test van strategie initialisatie"""
    exchange_manager = ExchangeManager()
    strategy_manager = StrategyManager(exchange_manager)
    await strategy_manager.initialize_strategies()
    assert len(strategy_manager.strategies) > 0

@pytest.mark.asyncio
async def test_risk_calculation():
    """Test van risico berekening"""
    exchange_manager = ExchangeManager()
    risk_manager = RiskManager(exchange_manager)
    result = await risk_manager.calculate_position_size("binance", "BTC/USDT", Decimal("100"))
    assert result is not None

@pytest.mark.asyncio
async def test_bot_startup():
    """Test van bot initialisatie"""
    from main import main
    await main()
    # To<PERSON><PERSON><PERSON> van specifieke asserts op basis van bot status
    assert True  # Voorlopige placeholder
