from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_amm_market = publicGetAmmMarket = Entry('amm/market', 'public', 'GET', {'cost': 1})
    public_get_common_currency_rate = publicGetCommonCurrencyRate = Entry('common/currency/rate', 'public', 'GET', {'cost': 1})
    public_get_common_asset_config = publicGetCommonAssetConfig = Entry('common/asset/config', 'public', 'GET', {'cost': 1})
    public_get_common_maintain_info = publicGetCommonMaintainInfo = Entry('common/maintain/info', 'public', 'GET', {'cost': 1})
    public_get_common_temp_maintain_info = publicGetCommonTempMaintainInfo = Entry('common/temp-maintain/info', 'public', 'GET', {'cost': 1})
    public_get_margin_market = publicGetMarginMarket = Entry('margin/market', 'public', 'GET', {'cost': 1})
    public_get_market_info = publicGetMarketInfo = Entry('market/info', 'public', 'GET', {'cost': 1})
    public_get_market_list = publicGetMarketList = Entry('market/list', 'public', 'GET', {'cost': 1})
    public_get_market_ticker = publicGetMarketTicker = Entry('market/ticker', 'public', 'GET', {'cost': 1})
    public_get_market_ticker_all = publicGetMarketTickerAll = Entry('market/ticker/all', 'public', 'GET', {'cost': 1})
    public_get_market_depth = publicGetMarketDepth = Entry('market/depth', 'public', 'GET', {'cost': 1})
    public_get_market_deals = publicGetMarketDeals = Entry('market/deals', 'public', 'GET', {'cost': 1})
    public_get_market_kline = publicGetMarketKline = Entry('market/kline', 'public', 'GET', {'cost': 1})
    public_get_market_detail = publicGetMarketDetail = Entry('market/detail', 'public', 'GET', {'cost': 1})
    private_get_account_amm_balance = privateGetAccountAmmBalance = Entry('account/amm/balance', 'private', 'GET', {'cost': 40})
    private_get_account_investment_balance = privateGetAccountInvestmentBalance = Entry('account/investment/balance', 'private', 'GET', {'cost': 40})
    private_get_account_balance_history = privateGetAccountBalanceHistory = Entry('account/balance/history', 'private', 'GET', {'cost': 40})
    private_get_account_market_fee = privateGetAccountMarketFee = Entry('account/market/fee', 'private', 'GET', {'cost': 40})
    private_get_balance_coin_deposit = privateGetBalanceCoinDeposit = Entry('balance/coin/deposit', 'private', 'GET', {'cost': 40})
    private_get_balance_coin_withdraw = privateGetBalanceCoinWithdraw = Entry('balance/coin/withdraw', 'private', 'GET', {'cost': 40})
    private_get_balance_info = privateGetBalanceInfo = Entry('balance/info', 'private', 'GET', {'cost': 40})
    private_get_balance_deposit_address_coin_type = privateGetBalanceDepositAddressCoinType = Entry('balance/deposit/address/{coin_type}', 'private', 'GET', {'cost': 40})
    private_get_contract_transfer_history = privateGetContractTransferHistory = Entry('contract/transfer/history', 'private', 'GET', {'cost': 40})
    private_get_credit_info = privateGetCreditInfo = Entry('credit/info', 'private', 'GET', {'cost': 40})
    private_get_credit_balance = privateGetCreditBalance = Entry('credit/balance', 'private', 'GET', {'cost': 40})
    private_get_investment_transfer_history = privateGetInvestmentTransferHistory = Entry('investment/transfer/history', 'private', 'GET', {'cost': 40})
    private_get_margin_account = privateGetMarginAccount = Entry('margin/account', 'private', 'GET', {'cost': 1})
    private_get_margin_config = privateGetMarginConfig = Entry('margin/config', 'private', 'GET', {'cost': 1})
    private_get_margin_loan_history = privateGetMarginLoanHistory = Entry('margin/loan/history', 'private', 'GET', {'cost': 40})
    private_get_margin_transfer_history = privateGetMarginTransferHistory = Entry('margin/transfer/history', 'private', 'GET', {'cost': 40})
    private_get_order_deals = privateGetOrderDeals = Entry('order/deals', 'private', 'GET', {'cost': 40})
    private_get_order_finished = privateGetOrderFinished = Entry('order/finished', 'private', 'GET', {'cost': 40})
    private_get_order_pending = privateGetOrderPending = Entry('order/pending', 'private', 'GET', {'cost': 8})
    private_get_order_status = privateGetOrderStatus = Entry('order/status', 'private', 'GET', {'cost': 8})
    private_get_order_status_batch = privateGetOrderStatusBatch = Entry('order/status/batch', 'private', 'GET', {'cost': 8})
    private_get_order_user_deals = privateGetOrderUserDeals = Entry('order/user/deals', 'private', 'GET', {'cost': 40})
    private_get_order_stop_finished = privateGetOrderStopFinished = Entry('order/stop/finished', 'private', 'GET', {'cost': 40})
    private_get_order_stop_pending = privateGetOrderStopPending = Entry('order/stop/pending', 'private', 'GET', {'cost': 8})
    private_get_order_user_trade_fee = privateGetOrderUserTradeFee = Entry('order/user/trade/fee', 'private', 'GET', {'cost': 1})
    private_get_order_market_trade_info = privateGetOrderMarketTradeInfo = Entry('order/market/trade/info', 'private', 'GET', {'cost': 1})
    private_get_sub_account_balance = privateGetSubAccountBalance = Entry('sub_account/balance', 'private', 'GET', {'cost': 1})
    private_get_sub_account_transfer_history = privateGetSubAccountTransferHistory = Entry('sub_account/transfer/history', 'private', 'GET', {'cost': 40})
    private_get_sub_account_auth_api = privateGetSubAccountAuthApi = Entry('sub_account/auth/api', 'private', 'GET', {'cost': 40})
    private_get_sub_account_auth_api_user_auth_id = privateGetSubAccountAuthApiUserAuthId = Entry('sub_account/auth/api/{user_auth_id}', 'private', 'GET', {'cost': 40})
    private_post_balance_coin_withdraw = privatePostBalanceCoinWithdraw = Entry('balance/coin/withdraw', 'private', 'POST', {'cost': 40})
    private_post_contract_balance_transfer = privatePostContractBalanceTransfer = Entry('contract/balance/transfer', 'private', 'POST', {'cost': 40})
    private_post_margin_flat = privatePostMarginFlat = Entry('margin/flat', 'private', 'POST', {'cost': 40})
    private_post_margin_loan = privatePostMarginLoan = Entry('margin/loan', 'private', 'POST', {'cost': 40})
    private_post_margin_transfer = privatePostMarginTransfer = Entry('margin/transfer', 'private', 'POST', {'cost': 40})
    private_post_order_limit_batch = privatePostOrderLimitBatch = Entry('order/limit/batch', 'private', 'POST', {'cost': 40})
    private_post_order_ioc = privatePostOrderIoc = Entry('order/ioc', 'private', 'POST', {'cost': 13.334})
    private_post_order_limit = privatePostOrderLimit = Entry('order/limit', 'private', 'POST', {'cost': 13.334})
    private_post_order_market = privatePostOrderMarket = Entry('order/market', 'private', 'POST', {'cost': 13.334})
    private_post_order_modify = privatePostOrderModify = Entry('order/modify', 'private', 'POST', {'cost': 13.334})
    private_post_order_stop_limit = privatePostOrderStopLimit = Entry('order/stop/limit', 'private', 'POST', {'cost': 13.334})
    private_post_order_stop_market = privatePostOrderStopMarket = Entry('order/stop/market', 'private', 'POST', {'cost': 13.334})
    private_post_order_stop_modify = privatePostOrderStopModify = Entry('order/stop/modify', 'private', 'POST', {'cost': 13.334})
    private_post_sub_account_transfer = privatePostSubAccountTransfer = Entry('sub_account/transfer', 'private', 'POST', {'cost': 40})
    private_post_sub_account_register = privatePostSubAccountRegister = Entry('sub_account/register', 'private', 'POST', {'cost': 1})
    private_post_sub_account_unfrozen = privatePostSubAccountUnfrozen = Entry('sub_account/unfrozen', 'private', 'POST', {'cost': 40})
    private_post_sub_account_frozen = privatePostSubAccountFrozen = Entry('sub_account/frozen', 'private', 'POST', {'cost': 40})
    private_post_sub_account_auth_api = privatePostSubAccountAuthApi = Entry('sub_account/auth/api', 'private', 'POST', {'cost': 40})
    private_put_balance_deposit_address_coin_type = privatePutBalanceDepositAddressCoinType = Entry('balance/deposit/address/{coin_type}', 'private', 'PUT', {'cost': 40})
    private_put_sub_account_unfrozen = privatePutSubAccountUnfrozen = Entry('sub_account/unfrozen', 'private', 'PUT', {'cost': 40})
    private_put_sub_account_frozen = privatePutSubAccountFrozen = Entry('sub_account/frozen', 'private', 'PUT', {'cost': 40})
    private_put_sub_account_auth_api_user_auth_id = privatePutSubAccountAuthApiUserAuthId = Entry('sub_account/auth/api/{user_auth_id}', 'private', 'PUT', {'cost': 40})
    private_put_v1_account_settings = privatePutV1AccountSettings = Entry('v1/account/settings', 'private', 'PUT', {'cost': 40})
    private_delete_balance_coin_withdraw = privateDeleteBalanceCoinWithdraw = Entry('balance/coin/withdraw', 'private', 'DELETE', {'cost': 40})
    private_delete_order_pending_batch = privateDeleteOrderPendingBatch = Entry('order/pending/batch', 'private', 'DELETE', {'cost': 40})
    private_delete_order_pending = privateDeleteOrderPending = Entry('order/pending', 'private', 'DELETE', {'cost': 13.334})
    private_delete_order_stop_pending = privateDeleteOrderStopPending = Entry('order/stop/pending', 'private', 'DELETE', {'cost': 40})
    private_delete_order_stop_pending_id = privateDeleteOrderStopPendingId = Entry('order/stop/pending/{id}', 'private', 'DELETE', {'cost': 13.334})
    private_delete_order_pending_by_client_id = privateDeleteOrderPendingByClientId = Entry('order/pending/by_client_id', 'private', 'DELETE', {'cost': 40})
    private_delete_order_stop_pending_by_client_id = privateDeleteOrderStopPendingByClientId = Entry('order/stop/pending/by_client_id', 'private', 'DELETE', {'cost': 40})
    private_delete_sub_account_auth_api_user_auth_id = privateDeleteSubAccountAuthApiUserAuthId = Entry('sub_account/auth/api/{user_auth_id}', 'private', 'DELETE', {'cost': 40})
    private_delete_sub_account_authorize_id = privateDeleteSubAccountAuthorizeId = Entry('sub_account/authorize/{id}', 'private', 'DELETE', {'cost': 40})
    perpetualpublic_get_ping = perpetualPublicGetPing = Entry('ping', 'perpetualPublic', 'GET', {'cost': 1})
    perpetualpublic_get_time = perpetualPublicGetTime = Entry('time', 'perpetualPublic', 'GET', {'cost': 1})
    perpetualpublic_get_market_list = perpetualPublicGetMarketList = Entry('market/list', 'perpetualPublic', 'GET', {'cost': 1})
    perpetualpublic_get_market_limit_config = perpetualPublicGetMarketLimitConfig = Entry('market/limit_config', 'perpetualPublic', 'GET', {'cost': 1})
    perpetualpublic_get_market_ticker = perpetualPublicGetMarketTicker = Entry('market/ticker', 'perpetualPublic', 'GET', {'cost': 1})
    perpetualpublic_get_market_ticker_all = perpetualPublicGetMarketTickerAll = Entry('market/ticker/all', 'perpetualPublic', 'GET', {'cost': 1})
    perpetualpublic_get_market_depth = perpetualPublicGetMarketDepth = Entry('market/depth', 'perpetualPublic', 'GET', {'cost': 1})
    perpetualpublic_get_market_deals = perpetualPublicGetMarketDeals = Entry('market/deals', 'perpetualPublic', 'GET', {'cost': 1})
    perpetualpublic_get_market_funding_history = perpetualPublicGetMarketFundingHistory = Entry('market/funding_history', 'perpetualPublic', 'GET', {'cost': 1})
    perpetualpublic_get_market_kline = perpetualPublicGetMarketKline = Entry('market/kline', 'perpetualPublic', 'GET', {'cost': 1})
    perpetualprivate_get_market_user_deals = perpetualPrivateGetMarketUserDeals = Entry('market/user_deals', 'perpetualPrivate', 'GET', {'cost': 1})
    perpetualprivate_get_asset_query = perpetualPrivateGetAssetQuery = Entry('asset/query', 'perpetualPrivate', 'GET', {'cost': 40})
    perpetualprivate_get_order_pending = perpetualPrivateGetOrderPending = Entry('order/pending', 'perpetualPrivate', 'GET', {'cost': 8})
    perpetualprivate_get_order_finished = perpetualPrivateGetOrderFinished = Entry('order/finished', 'perpetualPrivate', 'GET', {'cost': 40})
    perpetualprivate_get_order_stop_finished = perpetualPrivateGetOrderStopFinished = Entry('order/stop_finished', 'perpetualPrivate', 'GET', {'cost': 40})
    perpetualprivate_get_order_stop_pending = perpetualPrivateGetOrderStopPending = Entry('order/stop_pending', 'perpetualPrivate', 'GET', {'cost': 8})
    perpetualprivate_get_order_status = perpetualPrivateGetOrderStatus = Entry('order/status', 'perpetualPrivate', 'GET', {'cost': 8})
    perpetualprivate_get_order_stop_status = perpetualPrivateGetOrderStopStatus = Entry('order/stop_status', 'perpetualPrivate', 'GET', {'cost': 8})
    perpetualprivate_get_position_finished = perpetualPrivateGetPositionFinished = Entry('position/finished', 'perpetualPrivate', 'GET', {'cost': 40})
    perpetualprivate_get_position_pending = perpetualPrivateGetPositionPending = Entry('position/pending', 'perpetualPrivate', 'GET', {'cost': 40})
    perpetualprivate_get_position_funding = perpetualPrivateGetPositionFunding = Entry('position/funding', 'perpetualPrivate', 'GET', {'cost': 40})
    perpetualprivate_get_position_adl_history = perpetualPrivateGetPositionAdlHistory = Entry('position/adl_history', 'perpetualPrivate', 'GET', {'cost': 40})
    perpetualprivate_get_market_preference = perpetualPrivateGetMarketPreference = Entry('market/preference', 'perpetualPrivate', 'GET', {'cost': 40})
    perpetualprivate_get_position_margin_history = perpetualPrivateGetPositionMarginHistory = Entry('position/margin_history', 'perpetualPrivate', 'GET', {'cost': 40})
    perpetualprivate_get_position_settle_history = perpetualPrivateGetPositionSettleHistory = Entry('position/settle_history', 'perpetualPrivate', 'GET', {'cost': 40})
    perpetualprivate_post_market_adjust_leverage = perpetualPrivatePostMarketAdjustLeverage = Entry('market/adjust_leverage', 'perpetualPrivate', 'POST', {'cost': 1})
    perpetualprivate_post_market_position_expect = perpetualPrivatePostMarketPositionExpect = Entry('market/position_expect', 'perpetualPrivate', 'POST', {'cost': 1})
    perpetualprivate_post_order_put_limit = perpetualPrivatePostOrderPutLimit = Entry('order/put_limit', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_order_put_market = perpetualPrivatePostOrderPutMarket = Entry('order/put_market', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_order_put_stop_limit = perpetualPrivatePostOrderPutStopLimit = Entry('order/put_stop_limit', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_order_put_stop_market = perpetualPrivatePostOrderPutStopMarket = Entry('order/put_stop_market', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_order_modify = perpetualPrivatePostOrderModify = Entry('order/modify', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_order_modify_stop = perpetualPrivatePostOrderModifyStop = Entry('order/modify_stop', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_order_cancel = perpetualPrivatePostOrderCancel = Entry('order/cancel', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_order_cancel_all = perpetualPrivatePostOrderCancelAll = Entry('order/cancel_all', 'perpetualPrivate', 'POST', {'cost': 40})
    perpetualprivate_post_order_cancel_batch = perpetualPrivatePostOrderCancelBatch = Entry('order/cancel_batch', 'perpetualPrivate', 'POST', {'cost': 40})
    perpetualprivate_post_order_cancel_stop = perpetualPrivatePostOrderCancelStop = Entry('order/cancel_stop', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_order_cancel_stop_all = perpetualPrivatePostOrderCancelStopAll = Entry('order/cancel_stop_all', 'perpetualPrivate', 'POST', {'cost': 40})
    perpetualprivate_post_order_close_limit = perpetualPrivatePostOrderCloseLimit = Entry('order/close_limit', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_order_close_market = perpetualPrivatePostOrderCloseMarket = Entry('order/close_market', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_position_adjust_margin = perpetualPrivatePostPositionAdjustMargin = Entry('position/adjust_margin', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_position_stop_loss = perpetualPrivatePostPositionStopLoss = Entry('position/stop_loss', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_position_take_profit = perpetualPrivatePostPositionTakeProfit = Entry('position/take_profit', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_position_market_close = perpetualPrivatePostPositionMarketClose = Entry('position/market_close', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_order_cancel_by_client_id = perpetualPrivatePostOrderCancelByClientId = Entry('order/cancel/by_client_id', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_order_cancel_stop_by_client_id = perpetualPrivatePostOrderCancelStopByClientId = Entry('order/cancel_stop/by_client_id', 'perpetualPrivate', 'POST', {'cost': 20})
    perpetualprivate_post_market_preference = perpetualPrivatePostMarketPreference = Entry('market/preference', 'perpetualPrivate', 'POST', {'cost': 20})
