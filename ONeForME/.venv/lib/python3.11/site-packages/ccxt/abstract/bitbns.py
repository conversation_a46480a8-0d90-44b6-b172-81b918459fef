from ccxt.base.types import Entry


class ImplicitAPI:
    www_get_order_fetchmarkets = wwwGetOrderFetchMarkets = Entry('order/fetchMarkets', 'www', 'GET', {})
    www_get_order_fetchtickers = wwwGetOrderFetchTickers = Entry('order/fetchTickers', 'www', 'GET', {})
    www_get_order_fetchorderbook = wwwGetOrderFetchOrderbook = Entry('order/fetchOrderbook', 'www', 'GET', {})
    www_get_order_gettickerwithvolume = wwwGetOrderGetTickerWithVolume = Entry('order/getTickerWithVolume', 'www', 'GET', {})
    www_get_exchangedata_ohlc = wwwGetExchangeDataOhlc = Entry('exchangeData/ohlc', 'www', 'GET', {})
    www_get_exchangedata_orderbook = wwwGetExchangeDataOrderBook = Entry('exchangeData/orderBook', 'www', 'GET', {})
    www_get_exchangedata_tradedetails = wwwGetExchangeDataTradedetails = Entry('exchangeData/tradedetails', 'www', 'GET', {})
    v1_get_platform_status = v1GetPlatformStatus = Entry('platform/status', 'v1', 'GET', {})
    v1_get_tickers = v1GetTickers = Entry('tickers', 'v1', 'GET', {})
    v1_get_orderbook_sell_symbol = v1GetOrderbookSellSymbol = Entry('orderbook/sell/{symbol}', 'v1', 'GET', {})
    v1_get_orderbook_buy_symbol = v1GetOrderbookBuySymbol = Entry('orderbook/buy/{symbol}', 'v1', 'GET', {})
    v1_post_currentcoinbalance_everything = v1PostCurrentCoinBalanceEVERYTHING = Entry('currentCoinBalance/EVERYTHING', 'v1', 'POST', {})
    v1_post_getapiusagestatus_usage = v1PostGetApiUsageStatusUSAGE = Entry('getApiUsageStatus/USAGE', 'v1', 'POST', {})
    v1_post_getordersockettoken_usage = v1PostGetOrderSocketTokenUSAGE = Entry('getOrderSocketToken/USAGE', 'v1', 'POST', {})
    v1_post_currentcoinbalance_symbol = v1PostCurrentCoinBalanceSymbol = Entry('currentCoinBalance/{symbol}', 'v1', 'POST', {})
    v1_post_orderstatus_symbol = v1PostOrderStatusSymbol = Entry('orderStatus/{symbol}', 'v1', 'POST', {})
    v1_post_deposithistory_symbol = v1PostDepositHistorySymbol = Entry('depositHistory/{symbol}', 'v1', 'POST', {})
    v1_post_withdrawhistory_symbol = v1PostWithdrawHistorySymbol = Entry('withdrawHistory/{symbol}', 'v1', 'POST', {})
    v1_post_withdrawhistoryall_symbol = v1PostWithdrawHistoryAllSymbol = Entry('withdrawHistoryAll/{symbol}', 'v1', 'POST', {})
    v1_post_deposithistoryall_symbol = v1PostDepositHistoryAllSymbol = Entry('depositHistoryAll/{symbol}', 'v1', 'POST', {})
    v1_post_listopenorders_symbol = v1PostListOpenOrdersSymbol = Entry('listOpenOrders/{symbol}', 'v1', 'POST', {})
    v1_post_listopenstoporders_symbol = v1PostListOpenStopOrdersSymbol = Entry('listOpenStopOrders/{symbol}', 'v1', 'POST', {})
    v1_post_getcoinaddress_symbol = v1PostGetCoinAddressSymbol = Entry('getCoinAddress/{symbol}', 'v1', 'POST', {})
    v1_post_placesellorder_symbol = v1PostPlaceSellOrderSymbol = Entry('placeSellOrder/{symbol}', 'v1', 'POST', {})
    v1_post_placebuyorder_symbol = v1PostPlaceBuyOrderSymbol = Entry('placeBuyOrder/{symbol}', 'v1', 'POST', {})
    v1_post_buystoploss_symbol = v1PostBuyStopLossSymbol = Entry('buyStopLoss/{symbol}', 'v1', 'POST', {})
    v1_post_sellstoploss_symbol = v1PostSellStopLossSymbol = Entry('sellStopLoss/{symbol}', 'v1', 'POST', {})
    v1_post_cancelorder_symbol = v1PostCancelOrderSymbol = Entry('cancelOrder/{symbol}', 'v1', 'POST', {})
    v1_post_cancelstoplossorder_symbol = v1PostCancelStopLossOrderSymbol = Entry('cancelStopLossOrder/{symbol}', 'v1', 'POST', {})
    v1_post_listexecutedorders_symbol = v1PostListExecutedOrdersSymbol = Entry('listExecutedOrders/{symbol}', 'v1', 'POST', {})
    v1_post_placemarketorder_symbol = v1PostPlaceMarketOrderSymbol = Entry('placeMarketOrder/{symbol}', 'v1', 'POST', {})
    v1_post_placemarketorderqnty_symbol = v1PostPlaceMarketOrderQntySymbol = Entry('placeMarketOrderQnty/{symbol}', 'v1', 'POST', {})
    v2_post_orders = v2PostOrders = Entry('orders', 'v2', 'POST', {})
    v2_post_cancel = v2PostCancel = Entry('cancel', 'v2', 'POST', {})
    v2_post_getordersnew = v2PostGetordersnew = Entry('getordersnew', 'v2', 'POST', {})
    v2_post_marginorders = v2PostMarginOrders = Entry('marginOrders', 'v2', 'POST', {})
