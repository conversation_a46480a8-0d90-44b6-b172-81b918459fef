from typing import Dict, Optional
from core.strategy_manager import BaseStrategy
import pandas as pd

class ExampleRSIMACD(BaseStrategy):
    def __init__(self):
        super().__init__()
        self.rsi_period = 14
        self.macd_fast = 12
        self.macd_slow = 26
        self.signal_period = 9

    async def should_enter(self, exchange: str, symbol: str) -> bool:
        """Bepaalt of een positie moet worden ingenomen"""
        data = await self.get_recent_data(exchange, symbol)
        if not data:
            return False
            
        # RSI berekening
        delta = data['close'].diff(1)
        up, down = delta.copy(), delta.copy()
        up[up < 0] = 0
        down[down > 0] = 0
        roll_up = up.ewm(com=self.rsi_period - 1, adjust=False).mean()
        roll_down = down.ewm(com=self.rsi_period - 1, adjust=False).mean().abs()
        rs = roll_up / roll_down
        rsi = 100.0 - (100.0 / (1.0 + rs))
        
        # MACD berekening
        ema_fast = data['close'].ewm(span=self.macd_fast, adjust=False).mean()
        ema_slow = data['close'].ewm(span=self.macd_slow, adjust=False).mean()
        macd = ema_fast - ema_slow
        signal = macd.ewm(span=self.signal_period, adjust=False).mean()
        
        # Handelslogica
        if macd.iloc[-1] > signal.iloc[-1] and rsi.iloc[-1] < 30:
            return True
        return False

    async def should_exit(self, exchange: str, symbol: str) -> bool:
        """Bepaalt of een positie moet worden afgesloten"""
        data = await self.get_recent_data(exchange, symbol)
        if not data:
            return False
            
        # RSI berekening
        delta = data['close'].diff(1)
        up, down = delta.copy(), delta.copy()
        up[up < 0] = 0
        down[down > 0] = 0
        roll_up = up.ewm(com=self.rsi_period - 1, adjust=False).mean()
        roll_down = down.ewm(com=self.rsi_period - 1, adjust=False).mean().abs()
        rs = roll_up / roll_down
        rsi = 100.0 - (100.0 / (1.0 + rs))
        
        # MACD berekening
        ema_fast = data['close'].ewm(span=self.macd_fast, adjust=False).mean()
        ema_slow = data['close'].ewm(span=self.macd_slow, adjust=False).mean()
        macd = ema_fast - ema_slow
        signal = macd.ewm(span=self.signal_period, adjust=False).mean()
        
        # Handelslogica
        if macd.iloc[-1] < signal.iloc[-1] and rsi.iloc[-1] > 70:
            return True
        return False

    async def on_tick(self, exchange: str, symbol: str):
        """Wordt aangeroepen bij elke tick"""
        if await self.should_enter(exchange, symbol):
            print(f"RSI en MACD geven koopsignaal voor {symbol}")
        elif await self.should_exit(exchange, symbol):
            print(f"RSI en MACD geven verkoopsignaal voor {symbol}")
