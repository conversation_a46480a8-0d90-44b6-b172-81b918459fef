from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import CallbackContext, CommandHandler, MessageHandler, filters
from core.exchange_manager import ExchangeManager
from core.strategy_manager import StrategyManager
from core.risk_manager import RiskManager
from config.settings import settings
from loguru import logger

def start_command(update: Update, context: CallbackContext):
    """Verwerkt het /start commando"""
    logger.info("Gebruiker heeft /start opgeroepen")
    keyboard = [
        [
            InlineKeyboardButton("📊 Status", callback_data="status"),
            InlineKeyboardButton("⚙️ Instellingen", callback_data="settings")
        ],
        [
            InlineKeyboardButton("📈 Monitoring", callback_data="monitoring")
        ]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    context.bot.send_message(
        chat_id=update.effective_chat.id,
        text="Trading bot is gestart!\n"
             "Selecteer een optie:",
        reply_markup=reply_markup
    )

def status_command(update: Update, context: CallbackContext):
    """Toont de huidige status van de bot"""
    logger.info("Gebruiker heeft /status opgeroepen")
    context.bot.send_message(
        chat_id=update.effective_chat.id,
        text="Bot is actief en functioneert correct."
    )

def monitoring_command(update: Update, context: CallbackContext):
    """Toont real-time monitoring data"""
    logger.info("Gebruiker heeft monitoring opgeroepen")
    context.bot.send_message(
        chat_id=update.effective_chat.id,
        text="Real-time monitoring is actief."
    )

def help_command(update: Update, context: CallbackContext):
    """Toont hulp informatie"""
    logger.info("Gebruiker heeft /help opgeroepen")
    context.bot.send_message(
        chat_id=update.effective_chat.id,
        text="Dit is de trading bot. Beschikbare commando's:\n"
             "/start - Start de bot\n"
             "/status - Toon status\n"
             "/help - Toon deze hulp\n"
             "/monitoring - Real-time monitoring"
    )

def unknown_command(update: Update, context: CallbackContext):
    """Verwerkt onbekende commando's"""
    logger.warning("Onbekend commando ontvangen")
    context.bot.send_message(
        chat_id=update.effective_chat.id,
        text="Onbekend commando. Gebruik /help voor meer informatie."
    )

def error_handler(update: Update, context: CallbackContext):
    """Foutafhandeling"""
    logger.error(f"Fout opgetreden: {context.error}")
