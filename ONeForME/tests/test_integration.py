import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from telegram import Update, Chat, User, Message
from telegram.ext import ContextTypes

from bot.handlers import (
    start_handler,
    status_handler,
    help_handler,
    monitoring_handler,
    unknown_handler,
    set_managers,
)
from core.exchange_manager import ExchangeManager
from core.strategy_manager import StrategyManager
from core.risk_manager import RiskManager


class TestIntegration:
    """Integratietests voor de volledige bot workflow"""

    @pytest.fixture
    def real_managers(self):
        """Echte manager instanties voor integratietests"""
        exchange_manager = ExchangeManager()
        strategy_manager = StrategyManager(exchange_manager)
        risk_manager = RiskManager(exchange_manager)

        # Stel managers in voor handlers
        set_managers(exchange_manager, strategy_manager, risk_manager)

        return exchange_manager, strategy_manager, risk_manager

    @pytest.fixture
    def mock_telegram_update(self):
        """Mock Telegram Update voor realistische tests"""
        update = MagicMock(spec=Update)

        # Mock chat
        chat = MagicMock(spec=Chat)
        chat.id = 123456789
        chat.type = "private"
        update.effective_chat = chat

        # Mock user
        user = MagicMock(spec=User)
        user.id = 987654321
        user.first_name = "Test"
        user.username = "testuser"
        update.effective_user = user

        # Mock message
        message = MagicMock(spec=Message)
        message.text = "/start"
        message.chat = chat
        message.from_user = user
        update.message = message

        return update

    @pytest.fixture
    def mock_telegram_context(self):
        """Mock Telegram Context voor realistische tests"""
        context = MagicMock(spec=ContextTypes.DEFAULT_TYPE)
        context.bot = AsyncMock()
        context.bot.send_message = AsyncMock()
        return context

    @pytest.mark.asyncio
    async def test_full_bot_workflow(
        self, real_managers, mock_telegram_update, mock_telegram_context
    ):
        """Test de volledige bot workflow van start tot status"""
        exchange_manager, strategy_manager, risk_manager = real_managers

        # Test 1: Start commando
        await start_handler.callback(mock_telegram_update, mock_telegram_context)

        # Controleer dat start bericht is verzonden
        assert mock_telegram_context.bot.send_message.called
        start_call = mock_telegram_context.bot.send_message.call_args
        assert "Trading bot is gestart!" in start_call[1]["text"]

        # Reset mock voor volgende test
        mock_telegram_context.bot.send_message.reset_mock()

        # Test 2: Status commando
        await status_handler.callback(mock_telegram_update, mock_telegram_context)

        # Controleer dat status bericht is verzonden
        assert mock_telegram_context.bot.send_message.called
        status_call = mock_telegram_context.bot.send_message.call_args
        assert "Bot is actief en functioneert correct" in status_call[1]["text"]

        # Reset mock voor volgende test
        mock_telegram_context.bot.send_message.reset_mock()

        # Test 3: Help commando
        await help_handler.callback(mock_telegram_update, mock_telegram_context)

        # Controleer dat help bericht is verzonden
        assert mock_telegram_context.bot.send_message.called
        help_call = mock_telegram_context.bot.send_message.call_args
        assert "/start" in help_call[1]["text"]
        assert "/status" in help_call[1]["text"]

    @pytest.mark.asyncio
    async def test_handler_objects_are_properly_configured(self):
        """Test dat alle handler objecten correct zijn geconfigureerd"""
        # Test dat alle handlers bestaan
        assert start_handler is not None
        assert status_handler is not None
        assert help_handler is not None
        assert monitoring_handler is not None
        assert unknown_handler is not None

        # Test dat handlers de juiste commando's hebben
        assert start_handler.commands == {"start"}
        assert status_handler.commands == {"status"}
        assert help_handler.commands == {"help"}
        assert monitoring_handler.commands == {"monitoring"}

        # Test dat unknown_handler een MessageHandler is voor commando's
        from telegram.ext import MessageHandler

        assert isinstance(unknown_handler, MessageHandler)

    @pytest.mark.asyncio
    async def test_managers_integration_with_handlers(
        self, real_managers, mock_telegram_update, mock_telegram_context
    ):
        """Test dat managers correct integreren met handlers"""
        exchange_manager, strategy_manager, risk_manager = real_managers

        # Test status commando met echte managers
        await status_handler.callback(mock_telegram_update, mock_telegram_context)

        # Controleer dat status informatie van managers wordt gebruikt
        status_call = mock_telegram_context.bot.send_message.call_args
        status_text = status_call[1]["text"]

        # Controleer dat alle manager secties aanwezig zijn
        assert "Exchange Status" in status_text
        assert "Strategy Status" in status_text
        assert "Risk Management" in status_text

    @pytest.mark.asyncio
    async def test_error_resilience(self, real_managers, mock_telegram_context):
        """Test dat de bot resilient is tegen fouten"""
        # Test met ongeldige update (geen effective_chat)
        invalid_update = MagicMock(spec=Update)
        invalid_update.effective_chat = None

        # Alle handlers zouden graceful moeten omgaan met ongeldige updates
        handlers = [
            start_handler.callback,
            status_handler.callback,
            help_handler.callback,
            monitoring_handler.callback,
            unknown_handler.callback,
        ]

        for handler in handlers:
            # Deze zouden geen exception moeten gooien
            await handler(invalid_update, mock_telegram_context)

            # En send_message zou niet aangeroepen moeten zijn
            mock_telegram_context.bot.send_message.assert_not_called()
            mock_telegram_context.bot.send_message.reset_mock()

    @pytest.mark.asyncio
    async def test_concurrent_handler_execution(
        self, real_managers, mock_telegram_context
    ):
        """Test dat handlers concurrent kunnen worden uitgevoerd"""
        # Maak meerdere updates
        updates = []
        for i in range(5):
            update = MagicMock(spec=Update)
            chat = MagicMock(spec=Chat)
            chat.id = 123456789 + i
            update.effective_chat = chat
            updates.append(update)

        # Voer handlers concurrent uit
        tasks = []
        for update in updates:
            task = asyncio.create_task(
                start_handler.callback(update, mock_telegram_context)
            )
            tasks.append(task)

        # Wacht tot alle taken zijn voltooid
        await asyncio.gather(*tasks)

        # Controleer dat alle berichten zijn verzonden
        assert mock_telegram_context.bot.send_message.call_count == 5

    @pytest.mark.asyncio
    async def test_settings_integration(
        self, real_managers, mock_telegram_update, mock_telegram_context
    ):
        """Test dat settings correct worden gebruikt in handlers"""
        from config.settings import settings

        # Test status commando om te zien of settings worden gebruikt
        await status_handler.callback(mock_telegram_update, mock_telegram_context)

        status_call = mock_telegram_context.bot.send_message.call_args
        status_text = status_call[1]["text"]

        # Controleer dat risk percentage uit settings wordt gebruikt
        assert f"{settings.RISK_PERCENT_PER_TRADE}%" in status_text

    @pytest.mark.asyncio
    async def test_logging_integration(
        self, real_managers, mock_telegram_update, mock_telegram_context
    ):
        """Test dat logging correct werkt in handlers"""
        with patch("bot.handlers.logger") as mock_logger:
            # Test verschillende handlers
            await start_handler.callback(mock_telegram_update, mock_telegram_context)
            await status_handler.callback(mock_telegram_update, mock_telegram_context)
            await help_handler.callback(mock_telegram_update, mock_telegram_context)

            # Controleer dat logger is aangeroepen
            assert mock_logger.info.call_count >= 3

    @pytest.mark.asyncio
    async def test_memory_usage_during_handler_execution(
        self, real_managers, mock_telegram_context
    ):
        """Test dat handlers geen geheugen lekken hebben"""
        import gc
        import sys

        # Maak veel updates
        updates = []
        for i in range(100):
            update = MagicMock(spec=Update)
            chat = MagicMock(spec=Chat)
            chat.id = i
            update.effective_chat = chat
            updates.append(update)

        # Meet geheugengebruik voor
        gc.collect()
        initial_objects = len(gc.get_objects())

        # Voer veel handler calls uit
        for update in updates:
            await start_handler.callback(update, mock_telegram_context)

        # Forceer garbage collection
        gc.collect()
        final_objects = len(gc.get_objects())

        # Het aantal objecten zou niet dramatisch moeten zijn toegenomen
        # (een kleine toename is normaal door test overhead)
        object_increase = final_objects - initial_objects
        assert object_increase < 5000, (
            f"Mogelijk geheugenlek: {object_increase} nieuwe objecten"
        )
