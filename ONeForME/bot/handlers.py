from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from telegram.ext import (
    ContextTypes,
    CommandHandler,
    MessageHandler,
    CallbackQueryHandler,
    filters,
)
from config.settings import settings
from loguru import logger

# Global managers - worden geïnitialiseerd vanuit main.py
exchange_manager = None
strategy_manager = None
risk_manager = None


def set_managers(em, sm, rm):
    """Stel de managers in vanuit main.py"""
    global exchange_manager, strategy_manager, risk_manager
    exchange_manager = em
    strategy_manager = sm
    risk_manager = rm


async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Verwerkt het /start commando"""
    logger.info("Gebruiker heeft /start opgeroepen")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    # Basis knoppen voor iedereen
    keyboard = [
        [
            InlineKeyboardButton("📊 Status", callback_data="btn_status"),
            InlineKeyboardButton("📈 Monitoring", callback_data="btn_monitoring"),
        ],
        [
            InlineKeyboardButton("ℹ️ Help", callback_data="btn_help"),
            InlineKeyboardButton("🔄 Refresh", callback_data="btn_refresh"),
        ],
    ]

    # Admin knoppen toevoegen als gebruiker admin is
    user_id = update.effective_user.id if update.effective_user else 0
    if user_id in settings.admin_user_ids_list:
        admin_row = [
            InlineKeyboardButton("⚙️ Instellingen", callback_data="btn_settings"),
            InlineKeyboardButton("🛡️ Veiligheid", callback_data="btn_safety"),
        ]
        keyboard.append(admin_row)

        if settings.LIVE_TRADING_ENABLED:
            emergency_row = [
                InlineKeyboardButton("🚨 EMERGENCY STOP", callback_data="btn_emergency")
            ]
            keyboard.append(emergency_row)

    reply_markup = InlineKeyboardMarkup(keyboard)

    welcome_text = "🤖 **TRADING BOT GESTART**\n\n"
    welcome_text += f"📊 Trading Mode: {'Live Trading' if settings.LIVE_TRADING_ENABLED else 'Paper Trading'}\n"
    welcome_text += f"⚡ Status: {'Actief' if not settings.EMERGENCY_STOP else 'Emergency Stop'}\n\n"
    welcome_text += "Selecteer een optie hieronder:"

    await context.bot.send_message(
        chat_id=update.effective_chat.id,
        text=welcome_text,
        reply_markup=reply_markup,
        parse_mode="Markdown",
    )


async def status_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Toont de huidige status van de bot"""
    logger.info("Gebruiker heeft /status opgeroepen")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    # Gebruik de managers om status te verkrijgen
    status_text = "Bot is actief en functioneert correct.\n\n"

    if exchange_manager:
        status_text += "📊 Exchange Status:\n"
        # Hier kun je later exchange status toevoegen
        status_text += "- Exchanges verbonden\n"

    if strategy_manager:
        status_text += "\n🎯 Strategy Status:\n"
        # Hier kun je later strategy status toevoegen
        status_text += "- Strategieën geladen\n"

    if risk_manager:
        status_text += "\n⚠️ Risk Management:\n"
        # Hier kun je later risk status toevoegen
        status_text += f"- Risk per trade: {settings.RISK_PERCENT_PER_TRADE}%\n"

    await context.bot.send_message(chat_id=update.effective_chat.id, text=status_text)


async def monitoring_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Toont real-time monitoring data"""
    logger.info("Gebruiker heeft monitoring opgeroepen")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    await context.bot.send_message(
        chat_id=update.effective_chat.id, text="Real-time monitoring is actief."
    )


async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Toont hulp informatie"""
    logger.info("Gebruiker heeft /help opgeroepen")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    help_text = "🤖 **TRADING BOT COMMANDO'S**\n\n"
    help_text += "**Basis commando's:**\n"
    help_text += "/start - Start de bot interface\n"
    help_text += "/status - Toon bot en manager status\n"
    help_text += "/help - Toon deze hulp\n"
    help_text += "/monitoring - Real-time monitoring\n\n"

    # Admin commando's alleen tonen aan admins
    user_id = update.effective_user.id if update.effective_user else 0
    if user_id in settings.admin_user_ids_list:
        help_text += "**Admin commando's:**\n"
        help_text += "/safety - Veiligheidscheck voor live trading\n"
        help_text += "/emergency - 🚨 STOP alle trading onmiddellijk\n\n"
        help_text += "⚠️ **Live trading status:**\n"
        help_text += f"Paper trading: {'Aan' if settings.PAPER_TRADING else 'Uit'}\n"
        help_text += (
            f"Live trading: {'Aan' if settings.LIVE_TRADING_ENABLED else 'Uit'}\n"
        )

    await context.bot.send_message(
        chat_id=update.effective_chat.id, text=help_text, parse_mode="Markdown"
    )


async def safety_check_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Voert veiligheidscheck uit voor live trading"""
    logger.info("Gebruiker heeft veiligheidscheck opgeroepen")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    # Controleer of gebruiker admin is
    user_id = update.effective_user.id if update.effective_user else 0
    if user_id not in settings.admin_user_ids_list:
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="❌ Alleen admins kunnen veiligheidscheck uitvoeren.",
        )
        return

    is_safe, errors = settings.is_live_trading_safe()

    if is_safe:
        status_text = "✅ **VEILIGHEIDSCHECK GESLAAGD**\n\n"
        status_text += "Live trading kan veilig worden gestart.\n\n"
        status_text += "**Huidige instellingen:**\n"
        status_text += f"• Risk per trade: {settings.RISK_PERCENT_PER_TRADE}%\n"
        status_text += f"• Max positions: {settings.MAX_CONCURRENT_POSITIONS}\n"
        status_text += f"• Max drawdown: {settings.MAX_DRAWDOWN_PERCENT}%\n"
        status_text += (
            f"• Paper trading: {'Aan' if settings.PAPER_TRADING else 'Uit'}\n"
        )
        status_text += (
            f"• Live trading: {'Aan' if settings.LIVE_TRADING_ENABLED else 'Uit'}\n"
        )
    else:
        status_text = "🚨 **VEILIGHEIDSCHECK GEFAALD**\n\n"
        status_text += "Live trading is NIET veilig om te starten!\n\n"
        status_text += "**Problemen gevonden:**\n"
        for i, error in enumerate(errors, 1):
            status_text += f"{i}. {error}\n"
        status_text += "\n❌ Los deze problemen op voordat je live trading start!"

    await context.bot.send_message(
        chat_id=update.effective_chat.id, text=status_text, parse_mode="Markdown"
    )


async def emergency_stop_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Emergency stop - stopt alle trading onmiddellijk"""
    logger.warning("EMERGENCY STOP opgeroepen!")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    # Controleer of gebruiker admin is
    user_id = update.effective_user.id if update.effective_user else 0
    if user_id not in settings.admin_user_ids_list:
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="❌ Alleen admins kunnen emergency stop activeren.",
        )
        return

    # Hier zou je alle open posities sluiten en trading stoppen
    # Voor nu alleen een waarschuwing

    await context.bot.send_message(
        chat_id=update.effective_chat.id,
        text="🚨 **EMERGENCY STOP GEACTIVEERD** 🚨\n\n"
        "Alle trading is gestopt!\n"
        "Alle open posities worden gesloten.\n\n"
        "⚠️ Herstart de bot om trading te hervatten.",
        parse_mode="Markdown",
    )


async def unknown_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Verwerkt onbekende commando's"""
    logger.warning("Onbekend commando ontvangen")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    await context.bot.send_message(
        chat_id=update.effective_chat.id,
        text="Onbekend commando. Gebruik /help voor meer informatie.",
    )


async def button_callback_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Verwerkt alle knop callbacks"""
    query = update.callback_query

    if not query:
        return

    await query.answer()  # Bevestig de knopdruk

    if not query.data:
        return

    user_id = query.from_user.id if query.from_user else 0
    chat_id = query.message.chat.id if query.message and query.message.chat else None

    if not chat_id:
        return

    # Route naar de juiste handler gebaseerd op callback_data
    if query.data == "btn_status":
        await send_status_message(chat_id, context, user_id)
    elif query.data == "btn_monitoring":
        await send_monitoring_message(chat_id, context, user_id)
    elif query.data == "btn_help":
        await send_help_message(chat_id, context, user_id)
    elif query.data == "btn_refresh":
        await send_main_menu(chat_id, context, user_id, "🔄 Menu ververst!")
    elif query.data == "btn_settings":
        await send_settings_message(chat_id, context, user_id)
    elif query.data == "btn_safety":
        await send_safety_check_message(chat_id, context, user_id)
    elif query.data == "btn_emergency":
        await send_emergency_stop_message(chat_id, context, user_id)


async def send_status_message(
    chat_id: int, context: ContextTypes.DEFAULT_TYPE, user_id: int
):
    """Stuur status bericht met knoppen"""
    status_text = "📊 **BOT STATUS**\n\n"

    if exchange_manager:
        status_text += "🔗 **Exchange Status:**\n"
        status_text += "• Exchanges verbonden\n"

    if strategy_manager:
        status_text += "\n🎯 **Strategy Status:**\n"
        status_text += "• Strategieën geladen\n"

    if risk_manager:
        status_text += "\n⚠️ **Risk Management:**\n"
        status_text += f"• Risk per trade: {settings.RISK_PERCENT_PER_TRADE}%\n"
        status_text += f"• Max positions: {settings.MAX_CONCURRENT_POSITIONS}\n"
        status_text += f"• Max drawdown: {settings.MAX_DRAWDOWN_PERCENT}%\n"

    status_text += f"\n📈 **Trading Mode:**\n"
    status_text += f"• Paper trading: {'Aan' if settings.PAPER_TRADING else 'Uit'}\n"
    status_text += (
        f"• Live trading: {'Aan' if settings.LIVE_TRADING_ENABLED else 'Uit'}\n"
    )

    # Terug knop
    keyboard = [
        [InlineKeyboardButton("🔙 Terug naar menu", callback_data="btn_refresh")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await context.bot.send_message(
        chat_id=chat_id,
        text=status_text,
        reply_markup=reply_markup,
        parse_mode="Markdown",
    )


async def send_monitoring_message(
    chat_id: int, context: ContextTypes.DEFAULT_TYPE, user_id: int
):
    """Stuur monitoring bericht met knoppen"""
    monitoring_text = "📈 **REAL-TIME MONITORING**\n\n"
    monitoring_text += "🔄 Monitoring is actief...\n\n"
    monitoring_text += "📊 **Huidige data:**\n"
    monitoring_text += "• Geen actieve trades\n"
    monitoring_text += "• Portfolio waarde: Wordt geladen...\n"
    monitoring_text += "• Dagelijkse P&L: Wordt geladen...\n"

    # Knoppen voor monitoring acties
    keyboard = [
        [
            InlineKeyboardButton("🔄 Refresh Data", callback_data="btn_monitoring"),
            InlineKeyboardButton("📊 Portfolio", callback_data="btn_portfolio"),
        ],
        [InlineKeyboardButton("🔙 Terug naar menu", callback_data="btn_refresh")],
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await context.bot.send_message(
        chat_id=chat_id,
        text=monitoring_text,
        reply_markup=reply_markup,
        parse_mode="Markdown",
    )


async def send_help_message(
    chat_id: int, context: ContextTypes.DEFAULT_TYPE, user_id: int
):
    """Stuur help bericht met knoppen"""
    help_text = "ℹ️ **HELP & COMMANDO'S**\n\n"
    help_text += "**📱 Knoppen gebruiken:**\n"
    help_text += "• Gebruik de knoppen hieronder voor snelle toegang\n"
    help_text += "• Typ commando's voor directe actie\n\n"
    help_text += "**⌨️ Beschikbare commando's:**\n"
    help_text += "/start - Toon hoofdmenu\n"
    help_text += "/status - Toon bot status\n"
    help_text += "/help - Toon deze hulp\n"

    if user_id in settings.admin_user_ids_list:
        help_text += "\n**👑 Admin commando's:**\n"
        help_text += "/safety - Veiligheidscheck\n"
        help_text += "/emergency - Emergency stop\n"

    keyboard = [
        [InlineKeyboardButton("🔙 Terug naar menu", callback_data="btn_refresh")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await context.bot.send_message(
        chat_id=chat_id,
        text=help_text,
        reply_markup=reply_markup,
        parse_mode="Markdown",
    )


async def send_settings_message(
    chat_id: int, context: ContextTypes.DEFAULT_TYPE, user_id: int
):
    """Stuur instellingen bericht (alleen voor admins)"""
    if user_id not in settings.admin_user_ids_list:
        await context.bot.send_message(
            chat_id=chat_id, text="❌ Alleen admins kunnen instellingen bekijken."
        )
        return

    settings_text = "⚙️ **BOT INSTELLINGEN**\n\n"
    settings_text += f"🎯 **Risk Management:**\n"
    settings_text += f"• Risk per trade: {settings.RISK_PERCENT_PER_TRADE}%\n"
    settings_text += f"• Max positions: {settings.MAX_CONCURRENT_POSITIONS}\n"
    settings_text += f"• Max drawdown: {settings.MAX_DRAWDOWN_PERCENT}%\n"
    settings_text += f"• Max daily trades: {settings.MAX_DAILY_TRADES}\n\n"

    settings_text += f"📈 **Trading:**\n"
    settings_text += f"• Trading pairs: {settings.TRADING_PAIRS}\n"
    settings_text += f"• Timeframe: {settings.TIMEFRAME}\n"
    settings_text += f"• Paper trading: {'Aan' if settings.PAPER_TRADING else 'Uit'}\n"
    settings_text += (
        f"• Live trading: {'Aan' if settings.LIVE_TRADING_ENABLED else 'Uit'}\n"
    )

    keyboard = [
        [
            InlineKeyboardButton("🛡️ Veiligheidscheck", callback_data="btn_safety"),
            InlineKeyboardButton("📊 Status", callback_data="btn_status"),
        ],
        [InlineKeyboardButton("🔙 Terug naar menu", callback_data="btn_refresh")],
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await context.bot.send_message(
        chat_id=chat_id,
        text=settings_text,
        reply_markup=reply_markup,
        parse_mode="Markdown",
    )


async def send_safety_check_message(
    chat_id: int, context: ContextTypes.DEFAULT_TYPE, user_id: int
):
    """Stuur veiligheidscheck bericht (alleen voor admins)"""
    if user_id not in settings.admin_user_ids_list:
        await context.bot.send_message(
            chat_id=chat_id, text="❌ Alleen admins kunnen veiligheidscheck uitvoeren."
        )
        return

    is_safe, errors = settings.is_live_trading_safe()

    if is_safe:
        safety_text = "✅ **VEILIGHEIDSCHECK GESLAAGD**\n\n"
        safety_text += "Live trading kan veilig worden gestart.\n\n"
        safety_text += "**Huidige instellingen:**\n"
        safety_text += f"• Risk per trade: {settings.RISK_PERCENT_PER_TRADE}%\n"
        safety_text += f"• Max positions: {settings.MAX_CONCURRENT_POSITIONS}\n"
        safety_text += f"• Max drawdown: {settings.MAX_DRAWDOWN_PERCENT}%\n"
    else:
        safety_text = "🚨 **VEILIGHEIDSCHECK GEFAALD**\n\n"
        safety_text += "Live trading is NIET veilig!\n\n"
        safety_text += "**Problemen:**\n"
        for i, error in enumerate(errors, 1):
            safety_text += f"{i}. {error}\n"

    keyboard = [
        [
            InlineKeyboardButton("🔄 Hercheck", callback_data="btn_safety"),
            InlineKeyboardButton("⚙️ Instellingen", callback_data="btn_settings"),
        ],
        [InlineKeyboardButton("🔙 Terug naar menu", callback_data="btn_refresh")],
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await context.bot.send_message(
        chat_id=chat_id,
        text=safety_text,
        reply_markup=reply_markup,
        parse_mode="Markdown",
    )


async def send_emergency_stop_message(
    chat_id: int, context: ContextTypes.DEFAULT_TYPE, user_id: int
):
    """Stuur emergency stop bericht (alleen voor admins)"""
    if user_id not in settings.admin_user_ids_list:
        await context.bot.send_message(
            chat_id=chat_id, text="❌ Alleen admins kunnen emergency stop activeren."
        )
        return

    emergency_text = "🚨 **EMERGENCY STOP GEACTIVEERD**\n\n"
    emergency_text += "⚠️ Alle trading is gestopt!\n"
    emergency_text += "📊 Alle open posities worden gesloten.\n\n"
    emergency_text += "🔄 Herstart de bot om trading te hervatten."

    keyboard = [
        [InlineKeyboardButton("🔙 Terug naar menu", callback_data="btn_refresh")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await context.bot.send_message(
        chat_id=chat_id,
        text=emergency_text,
        reply_markup=reply_markup,
        parse_mode="Markdown",
    )


async def send_main_menu(
    chat_id: int,
    context: ContextTypes.DEFAULT_TYPE,
    user_id: int,
    message: str | None = None,
):
    """Stuur hoofdmenu"""
    # Basis knoppen voor iedereen
    keyboard = [
        [
            InlineKeyboardButton("📊 Status", callback_data="btn_status"),
            InlineKeyboardButton("📈 Monitoring", callback_data="btn_monitoring"),
        ],
        [
            InlineKeyboardButton("ℹ️ Help", callback_data="btn_help"),
            InlineKeyboardButton("🔄 Refresh", callback_data="btn_refresh"),
        ],
    ]

    # Admin knoppen toevoegen
    if user_id in settings.admin_user_ids_list:
        admin_row = [
            InlineKeyboardButton("⚙️ Instellingen", callback_data="btn_settings"),
            InlineKeyboardButton("🛡️ Veiligheid", callback_data="btn_safety"),
        ]
        keyboard.append(admin_row)

        if settings.LIVE_TRADING_ENABLED:
            emergency_row = [
                InlineKeyboardButton("🚨 EMERGENCY STOP", callback_data="btn_emergency")
            ]
            keyboard.append(emergency_row)

    reply_markup = InlineKeyboardMarkup(keyboard)

    menu_text = message or "🤖 **TRADING BOT HOOFDMENU**"
    menu_text += f"\n\n📊 Mode: {'Live Trading' if settings.LIVE_TRADING_ENABLED else 'Paper Trading'}"
    menu_text += (
        f"\n⚡ Status: {'Actief' if not settings.EMERGENCY_STOP else 'Emergency Stop'}"
    )

    await context.bot.send_message(
        chat_id=chat_id,
        text=menu_text,
        reply_markup=reply_markup,
        parse_mode="Markdown",
    )


async def error_handler(_update: object, context: ContextTypes.DEFAULT_TYPE):
    """Foutafhandeling"""
    logger.error(f"Fout opgetreden: {context.error}")
    # Update parameter wordt niet gebruikt omdat het een object type is voor error handling


# Handler objecten voor registratie
start_handler = CommandHandler("start", start_command)
status_handler = CommandHandler("status", status_command)
help_handler = CommandHandler("help", help_command)
monitoring_handler = CommandHandler("monitoring", monitoring_command)
safety_check_handler = CommandHandler("safety", safety_check_command)
emergency_stop_handler = CommandHandler("emergency", emergency_stop_command)
callback_handler = CallbackQueryHandler(button_callback_handler)
unknown_handler = MessageHandler(filters.COMMAND, unknown_command)
