from typing import Dict, Optional
from pydantic import Decimal
from core.exchange_manager import ExchangeManager
from config.settings import settings
from loguru import logger

class RiskManager:
    def __init__(self, exchange_manager: ExchangeManager):
        self.exchange_manager = exchange_manager
        self.max_drawdown = Decimal(settings.RISK_PERCENT_PER_TRADE / 100)
        self.stop_loss_percent = Decimal(2.0)  # Standaard stop-loss van 2%
        self.take_profit_percent = Decimal(3.0)  # Standaard take-profit van 3%
        
    async def calculate_position_size(self, exchange: str, symbol: str, risk_amount: Decimal) -> Optional[Decimal]:
        """Bepaalt de positiegrootte gebaseerd op het risico en account equity"""
        try:
            account_balance = await self.exchange_manager.fetch_ticker(exchange, symbol)
            if not account_balance:
                return None
                
            equity = account_balance['close'] * Decimal(account_balance['base'])
            position_size = (risk_amount / self.max_drawdown) * equity
            return position_size
            
        except Exception as e:
            logger.error(f"Fout bij positiegrootte berekening: {e}")
            return None

    async def check_risk_tolerance(self, exchange: str, symbol: str) -> bool:
        """Controleert of de huidige drawdown binnen de toegestane risicotolerantie valt"""
        try:
            current_balance = await self.exchange_manager.fetch_ticker(exchange, symbol)
            if not current_balance:
                return False
                
            current_drawdown = (current_balance['close'] - current_balance['open']) / current_balance['open']
            return current_drawdown <= self.max_drawdown
            
        except Exception as e:
            logger.error(f"Fout bij risicotolerantie controle: {e}")
            return False

    async def set_stop_loss(self, exchange: str, symbol: str, price: float):
        """Stelt een stop-loss in"""
        try:
            await self.exchange_manager.exchanges[exchange].create_order(
                symbol=symbol,
                type='stop_loss_limit',
                side='sell',
                quantity='0.01',
                stop_price=price * (1 - self.stop_loss_percent / 100),
                price=price * (1 - self.stop_loss_percent / 100)
            )
            logger.info(f"Stop-loss ingesteld voor {symbol} bij {price}")
        except Exception as e:
            logger.error(f"Fout bij het instellen van stop-loss: {e}")

    async def set_take_profit(self, exchange: str, symbol: str, price: float):
        """Stelt een take-profit in"""
        try:
            await self.exchange_manager.exchanges[exchange].create_order(
                symbol=symbol,
                type='take_profit_limit',
                side='sell',
                quantity='0.01',
                stop_price=price * (1 + self.take_profit_percent / 100),
                price=price * (1 + self.take_profit_percent / 100)
            )
            logger.info(f"Take-profit ingesteld voor {symbol} bij {price}")
        except Exception as e:
            logger.error(f"Fout bij het instellen van take-profit: {e}")
