# Telegram Configuration
TELEGRAM_BOT_TOKEN=**********************************************
ADMIN_USER_IDS=7340670351,7899433889
VIEWER_USER_IDS=111111111,6438349353

# Exchange API Keys - KuCoin
KUCOIN_API_KEY=4EvMvOpoKyGi8t5J4tXSEz2zNp2GFxtVsr2rGGCUEvDTpnehdfdmATwJ9bClzhTd
KUCOIN_SECRET=6fd9d577-9707-4c9e-8fe2-baf2add9b174
KUCOIN_PASSWORD=Fatima23.
# Exchange API Keys - Binance
BINANCE_API_KEY=BB9LwgLzkht11CMdTBuNkl1Di63fIImuy4qOyvDAt1k7wqkPu1LhSIOmBj7OZM2q
BINANCE_SECRET=4EvMvOpoKyGi8t5J4tXSEz2zNp2GFxtVsr2rGGCUEvDTpnehdfdmATwJ9bClzhTd

# Exchange API Keys - Bybit
BYBIT_API_KEY=your_bybit_api_key
BYBIT_SECRET=your_bybit_secret

# Trading Configuration
TRADING_PAIRS=BTC/USDT,ETH/USDT,BNB/USDT,SOL/USDT,XRP/USDT
TIMEFRAME=5m
PAPER_TRADING=true

# Risk Management
RISK_PERCENT_PER_TRADE=2.0
MAX_CONCURRENT_POSITIONS=3
MAX_DRAWDOWN_PERCENT=10.0
POSITION_SIZE_TYPE=percentage

# Strategy Parameters
RSI_PERIOD=14
MACD_FAST=12
MACD_SLOW=26
MACD_SIGNAL=9

# Database
DATABASE_URL=sqlite+aiosqlite:///./trading_bot.db

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/trading_bot.log
