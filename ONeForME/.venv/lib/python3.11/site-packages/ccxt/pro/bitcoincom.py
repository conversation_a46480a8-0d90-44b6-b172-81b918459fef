# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.pro.hitbtc import hitbtc

import ccxt.async_support.bequant as bequantRest


class bitcoincom(hitbtc):

    def describe(self):
        # eslint-disable-next-line new-cap
        restInstance = bequantRest()
        restDescribe = restInstance.describe()
        extended = self.deep_extend(super(bitcoincom, self).describe(), restDescribe)
        return self.deep_extend(extended, {
            'id': 'bitcoincom',
            'name': 'bitcoin.com',
            'countries': ['KN'],
            'urls': {
                'logo': 'https://user-images.githubusercontent.com/1294454/97296144-514fa300-1861-11eb-952b-3d55d492200b.jpg',
                'api': {
                    'ws': 'wss://api.fmfw.io/api/2/ws',
                },
            },
            'fees': {
                'trading': {
                    'maker': self.parse_number('0.0015'),
                    'taker': self.parse_number('0.002'),
                },
            },
        })
