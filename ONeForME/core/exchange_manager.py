import ccxt.async_support as ccxt
from loguru import logger
from config.settings import settings
import asyncio

class ExchangeManager:
    def __init__(self):
        self.exchanges = {}

    async def initialize_exchanges(self):
        for exchange_name in ['kucoin', 'binance', 'bybit']:
            try:
                exchange = getattr(ccxt, exchange_name)({
                    'apiKey': getattr(settings, f'{exchange_name}_API_KEY', None),
                    'secret': getattr(settings, f'{exchange_name}_SECRET', None),
                    'password': getattr(settings, f'{exchange_name}_PASSWORD', None),
                })
                await exchange.load_markets()
                self.exchanges[exchange_name] = exchange
                logger.info(f'Initialized {exchange_name} exchange')
            except Exception as e:
                logger.error(f'Failed to initialize {exchange_name} exchange: {e}')

    async def fetch_ticker(self, exchange_name, symbol, retries=3, delay=1):
        """Fetch ticker data with simple retry mechanism"""
        if exchange_name not in self.exchanges:
            raise ValueError(f'Exchange {exchange_name} not initialized')
            
        for attempt in range(retries):
            try:
                return await self.exchanges[exchange_name].fetch_ticker(symbol)
            except Exception as e:
                if attempt == retries - 1:
                    raise
                await asyncio.sleep(delay)
                delay *= 2  # Exponential backoff

    async def close_exchanges(self):
        for exchange in self.exchanges.values():
            await exchange.close()

"""
Manages cryptocurrency exchanges by initializing, tracking, and interacting with multiple exchange platforms.

This class provides methods to:
- Initialize exchanges with API credentials
- Fetch ticker data with retry mechanism
- Safely close exchange connections

Attributes:
    exchanges (dict): A dictionary storing initialized exchange instances keyed by exchange name.
"""
exchange_manager = ExchangeManager()
