import random
import time

def fetch_market_data():
    # Simu<PERSON>rt het ophalen van marktdata (bijv. huidige prijs)
    return random.uniform(100, 200)

def decide_trade(price):
    # Eenvoudige strategie: koop als de prijs onder de 150 ligt, verkoop bij 150 of hoger.
    if price < 150:
        return 'buy'
    else:
        return 'sell'

def execute_trade(action, price):
    print(f"{'Koop' if action == 'buy' else 'Verkoop'} positie tegen prijs: {price:.2f}")

def trading_bot():
    # Simuleert 10 handelscycli
    for _ in range(10):
        price = fetch_market_data()
        action = decide_trade(price)
        execute_trade(action, price)
        time.sleep(1)

if __name__ == '__main__':
    trading_bot()