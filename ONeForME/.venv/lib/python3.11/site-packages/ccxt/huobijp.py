# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.base.exchange import Exchange
from ccxt.abstract.huobijp import ImplicitAPI
import hashlib
from ccxt.base.types import Balances, Currency, Int, Market, Order, OrderBook, OrderSide, OrderType, Str, Strings, Ticker, Tickers, Trade, Transaction
from typing import List
from ccxt.base.errors import ExchangeError
from ccxt.base.errors import PermissionDenied
from ccxt.base.errors import ArgumentsRequired
from ccxt.base.errors import BadRequest
from ccxt.base.errors import BadSymbol
from ccxt.base.errors import InsufficientFunds
from ccxt.base.errors import InvalidOrder
from ccxt.base.errors import OrderNotFound
from ccxt.base.errors import NotSupported
from ccxt.base.errors import NetworkError
from ccxt.base.errors import ExchangeNotAvailable
from ccxt.base.errors import OnMaintenance
from ccxt.base.errors import RequestTimeout
from ccxt.base.errors import AuthenticationError
from ccxt.base.decimal_to_precision import TRUNCATE
from ccxt.base.decimal_to_precision import TICK_SIZE
from ccxt.base.precise import Precise


class huobijp(Exchange, ImplicitAPI):

    def describe(self):
        return self.deep_extend(super(huobijp, self).describe(), {
            'id': 'huobijp',
            'name': 'Huobi Japan',
            'countries': ['JP'],
            'rateLimit': 100,
            'userAgent': self.userAgents['chrome39'],
            'certified': False,
            'version': 'v1',
            'hostname': 'api-cloud.bittrade.co.jp',
            'pro': True,
            'has': {
                'CORS': None,
                'spot': True,
                'margin': None,
                'swap': False,
                'future': False,
                'option': False,
                'cancelAllOrders': True,
                'cancelOrder': True,
                'cancelOrders': True,
                'createMarketBuyOrderWithCost': True,
                'createMarketOrderWithCost': False,
                'createMarketSellOrderWithCost': False,
                'createOrder': True,
                'createStopLimitOrder': False,
                'createStopMarketOrder': False,
                'createStopOrder': False,
                'fetchAccounts': True,
                'fetchBalance': True,
                'fetchClosedOrders': True,
                'fetchCurrencies': True,
                'fetchDepositAddress': False,
                'fetchDepositAddressesByNetwork': False,
                'fetchDeposits': True,
                'fetchFundingHistory': False,
                'fetchFundingRate': False,
                'fetchFundingRateHistory': False,
                'fetchFundingRates': False,
                'fetchIndexOHLCV': False,
                'fetchMarkets': True,
                'fetchMarkOHLCV': False,
                'fetchMyTrades': True,
                'fetchOHLCV': True,
                'fetchOpenOrders': True,
                'fetchOrder': True,
                'fetchOrderBook': True,
                'fetchOrders': True,
                'fetchOrderTrades': True,
                'fetchPremiumIndexOHLCV': False,
                'fetchTicker': True,
                'fetchTickers': True,
                'fetchTime': True,
                'fetchTrades': True,
                'fetchTradingLimits': True,
                'fetchWithdrawals': True,
                'withdraw': True,
            },
            'timeframes': {
                '1m': '1min',
                '5m': '5min',
                '15m': '15min',
                '30m': '30min',
                '1h': '60min',
                '4h': '4hour',
                '1d': '1day',
                '1w': '1week',
                '1M': '1mon',
                '1y': '1year',
            },
            'urls': {
                'logo': 'https://user-images.githubusercontent.com/1294454/85734211-85755480-b705-11ea-8b35-0b7f1db33a2f.jpg',
                'api': {
                    'market': 'https://{hostname}',
                    'public': 'https://{hostname}',
                    'private': 'https://{hostname}',
                    'v2Public': 'https://{hostname}',
                    'v2Private': 'https://{hostname}',
                },
                'www': 'https://www.huobi.co.jp',
                'referral': 'https://www.huobi.co.jp/register/?invite_code=znnq3',
                'doc': 'https://api-doc.huobi.co.jp',
                'fees': 'https://www.huobi.co.jp/support/fee',
            },
            'api': {
                'v2Public': {
                    'get': {
                        'reference/currencies': 1,  # 币链参考信息
                        'market-status': 1,  # 获取当前市场状态
                    },
                },
                'v2Private': {
                    'get': {
                        'account/ledger': 1,
                        'account/withdraw/quota': 1,
                        'account/withdraw/address': 1,  # 提币地址查询(限母用户可用)
                        'account/deposit/address': 1,
                        'account/repayment': 5,  # 还币交易记录查询
                        'reference/transact-fee-rate': 1,
                        'account/asset-valuation': 0.2,  # 获取账户资产估值
                        'point/account': 5,  # 点卡余额查询
                        'sub-user/user-list': 1,  # 获取子用户列表
                        'sub-user/user-state': 1,  # 获取特定子用户的用户状态
                        'sub-user/account-list': 1,  # 获取特定子用户的账户列表
                        'sub-user/deposit-address': 1,  # 子用户充币地址查询
                        'sub-user/query-deposit': 1,  # 子用户充币记录查询
                        'user/api-key': 1,  # 母子用户API key信息查询
                        'user/uid': 1,  # 母子用户获取用户UID
                        'algo-orders/opening': 1,  # 查询未触发OPEN策略委托
                        'algo-orders/history': 1,  # 查询策略委托历史
                        'algo-orders/specific': 1,  # 查询特定策略委托
                        'c2c/offers': 1,  # 查询借入借出订单
                        'c2c/offer': 1,  # 查询特定借入借出订单及其交易记录
                        'c2c/transactions': 1,  # 查询借入借出交易记录
                        'c2c/repayment': 1,  # 查询还币交易记录
                        'c2c/account': 1,  # 查询账户余额
                        'etp/reference': 1,  # 基础参考信息
                        'etp/transactions': 5,  # 获取杠杆ETP申赎记录
                        'etp/transaction': 5,  # 获取特定杠杆ETP申赎记录
                        'etp/rebalance': 1,  # 获取杠杆ETP调仓记录
                        'etp/limit': 1,  # 获取ETP持仓限额
                    },
                    'post': {
                        'account/transfer': 1,
                        'account/repayment': 5,  # 归还借币（全仓逐仓通用）
                        'point/transfer': 5,  # 点卡划转
                        'sub-user/management': 1,  # 冻结/解冻子用户
                        'sub-user/creation': 1,  # 子用户创建
                        'sub-user/tradable-market': 1,  # 设置子用户交易权限
                        'sub-user/transferability': 1,  # 设置子用户资产转出权限
                        'sub-user/api-key-generation': 1,  # 子用户API key创建
                        'sub-user/api-key-modification': 1,  # 修改子用户API key
                        'sub-user/api-key-deletion': 1,  # 删除子用户API key
                        'sub-user/deduct-mode': 1,  # 设置子用户手续费抵扣模式
                        'algo-orders': 1,  # 策略委托下单
                        'algo-orders/cancel-all-after': 1,  # 自动撤销订单
                        'algo-orders/cancellation': 1,  # 策略委托（触发前）撤单
                        'c2c/offer': 1,  # 借入借出下单
                        'c2c/cancellation': 1,  # 借入借出撤单
                        'c2c/cancel-all': 1,  # 撤销所有借入借出订单
                        'c2c/repayment': 1,  # 还币
                        'c2c/transfer': 1,  # 资产划转
                        'etp/creation': 5,  # 杠杆ETP换入
                        'etp/redemption': 5,  # 杠杆ETP换出
                        'etp/{transactId}/cancel': 10,  # 杠杆ETP单个撤单
                        'etp/batch-cancel': 50,  # 杠杆ETP批量撤单
                    },
                },
                'market': {
                    'get': {
                        'history/kline': 1,  # 获取K线数据
                        'detail/merged': 1,  # 获取聚合行情(Ticker)
                        'depth': 1,  # 获取 Market Depth 数据
                        'trade': 1,  # 获取 Trade Detail 数据
                        'history/trade': 1,  # 批量获取最近的交易记录
                        'detail': 1,  # 获取 Market Detail 24小时成交量数据
                        'tickers': 1,
                        'etp': 1,  # 获取杠杆ETP实时净值
                    },
                },
                'public': {
                    'get': {
                        'common/symbols': 1,  # 查询系统支持的所有交易对
                        'common/currencys': 1,  # 查询系统支持的所有币种
                        'common/timestamp': 1,  # 查询系统当前时间
                        'common/exchange': 1,  # order limits
                        'settings/currencys': 1,  # ?language=en-US
                    },
                },
                'private': {
                    'get': {
                        'account/accounts': 0.2,  # 查询当前用户的所有账户(即account-id)
                        'account/accounts/{id}/balance': 0.2,  # 查询指定账户的余额
                        'account/accounts/{sub-uid}': 1,
                        'account/history': 4,
                        'cross-margin/loan-info': 1,
                        'margin/loan-info': 1,  # 查询借币币息率及额度
                        'fee/fee-rate/get': 1,
                        'order/openOrders': 0.4,
                        'order/orders': 0.4,
                        'order/orders/{id}': 0.4,  # 查询某个订单详情
                        'order/orders/{id}/matchresults': 0.4,  # 查询某个订单的成交明细
                        'order/orders/getClientOrder': 0.4,
                        'order/history': 1,  # 查询当前委托、历史委托
                        'order/matchresults': 1,  # 查询当前成交、历史成交
                        # 'dw/withdraw-virtual/addresses',  # 查询虚拟币提现地址（Deprecated）
                        'query/deposit-withdraw': 1,
                        # 'margin/loan-info',  # duplicate
                        'margin/loan-orders': 0.2,  # 借贷订单
                        'margin/accounts/balance': 0.2,  # 借贷账户详情
                        'cross-margin/loan-orders': 1,  # 查询借币订单
                        'cross-margin/accounts/balance': 1,  # 借币账户详情
                        'points/actions': 1,
                        'points/orders': 1,
                        'subuser/aggregate-balance': 10,
                        'stable-coin/exchange_rate': 1,
                        'stable-coin/quote': 1,
                    },
                    'post': {
                        'account/transfer': 1,  # 资产划转(该节点为母用户和子用户进行资产划转的通用接口。)
                        'futures/transfer': 1,
                        'order/batch-orders': 0.4,
                        'order/orders/place': 0.2,  # 创建并执行一个新订单(一步下单， 推荐使用)
                        'order/orders/submitCancelClientOrder': 0.2,
                        'order/orders/batchCancelOpenOrders': 0.4,
                        # 'order/orders',  # 创建一个新的订单请求 （仅创建订单，不执行下单）
                        # 'order/orders/{id}/place',  # 执行一个订单 （仅执行已创建的订单）
                        'order/orders/{id}/submitcancel': 0.2,  # 申请撤销一个订单请求
                        'order/orders/batchcancel': 0.4,  # 批量撤销订单
                        # 'dw/balance/transfer',  # 资产划转
                        'dw/withdraw/api/create': 1,  # 申请提现虚拟币
                        # 'dw/withdraw-virtual/create',  # 申请提现虚拟币
                        # 'dw/withdraw-virtual/{id}/place',  # 确认申请虚拟币提现（Deprecated）
                        'dw/withdraw-virtual/{id}/cancel': 1,  # 申请取消提现虚拟币
                        'dw/transfer-in/margin': 10,  # 现货账户划入至借贷账户
                        'dw/transfer-out/margin': 10,  # 借贷账户划出至现货账户
                        'margin/orders': 10,  # 申请借贷
                        'margin/orders/{id}/repay': 10,  # 归还借贷
                        'cross-margin/transfer-in': 1,  # 资产划转
                        'cross-margin/transfer-out': 1,  # 资产划转
                        'cross-margin/orders': 1,  # 申请借币
                        'cross-margin/orders/{id}/repay': 1,  # 归还借币
                        'stable-coin/exchange': 1,
                        'subuser/transfer': 10,
                    },
                },
            },
            'fees': {
                'trading': {
                    'feeSide': 'get',
                    'tierBased': False,
                    'percentage': True,
                    'maker': self.parse_number('0.002'),
                    'taker': self.parse_number('0.002'),
                },
            },
            'precisionMode': TICK_SIZE,
            'exceptions': {
                'broad': {
                    'contract is restricted of closing positions on API.  Please contact customer service': OnMaintenance,
                    'maintain': OnMaintenance,
                },
                'exact': {
                    # err-code
                    'bad-request': BadRequest,
                    'base-date-limit-error': BadRequest,  # {"status":"error","err-code":"base-date-limit-error","err-msg":"date less than system limit","data":null}
                    'api-not-support-temp-addr': PermissionDenied,  # {"status":"error","err-code":"api-not-support-temp-addr","err-msg":"API withdrawal does not support temporary addresses","data":null}
                    'timeout': RequestTimeout,  # {"ts":*************,"status":"error","err-code":"timeout","err-msg":"Request Timeout"}
                    'gateway-internal-error': ExchangeNotAvailable,  # {"status":"error","err-code":"gateway-internal-error","err-msg":"Failed to load data. Try again later.","data":null}
                    'account-frozen-balance-insufficient-error': InsufficientFunds,  # {"status":"error","err-code":"account-frozen-balance-insufficient-error","err-msg":"trade account balance is not enough, left: `0.0027`","data":null}
                    'invalid-amount': InvalidOrder,  # eg "Paramemter `amount` is invalid."
                    'order-limitorder-amount-min-error': InvalidOrder,  # limit order amount error, min: `0.001`
                    'order-limitorder-amount-max-error': InvalidOrder,  # market order amount error, max: `1000000`
                    'order-marketorder-amount-min-error': InvalidOrder,  # market order amount error, min: `0.01`
                    'order-limitorder-price-min-error': InvalidOrder,  # limit order price error
                    'order-limitorder-price-max-error': InvalidOrder,  # limit order price error
                    'order-holding-limit-failed': InvalidOrder,  # {"status":"error","err-code":"order-holding-limit-failed","err-msg":"Order failed, exceeded the holding limit of self currency","data":null}
                    'order-orderprice-precision-error': InvalidOrder,  # {"status":"error","err-code":"order-orderprice-precision-error","err-msg":"order price precision error, scale: `4`","data":null}
                    'order-etp-nav-price-max-error': InvalidOrder,  # {"status":"error","err-code":"order-etp-nav-price-max-error","err-msg":"Order price cannot be higher than 5% of NAV","data":null}
                    'order-orderstate-error': OrderNotFound,  # canceling an already canceled order
                    'order-queryorder-invalid': OrderNotFound,  # querying a non-existent order
                    'order-update-error': ExchangeNotAvailable,  # undocumented error
                    'api-signature-check-failed': AuthenticationError,
                    'api-signature-not-valid': AuthenticationError,  # {"status":"error","err-code":"api-signature-not-valid","err-msg":"Signature not valid: Incorrect Access key [Access key错误]","data":null}
                    'base-record-invalid': OrderNotFound,  # https://github.com/ccxt/ccxt/issues/5750
                    'base-symbol-trade-disabled': BadSymbol,  # {"status":"error","err-code":"base-symbol-trade-disabled","err-msg":"Trading is disabled for self symbol","data":null}
                    'base-symbol-error': BadSymbol,  # {"status":"error","err-code":"base-symbol-error","err-msg":"The symbol is invalid","data":null}
                    'system-maintenance': OnMaintenance,  # {"status": "error", "err-code": "system-maintenance", "err-msg": "System is in maintenance!", "data": null}
                    # err-msg
                    'invalid symbol': BadSymbol,  # {"ts":1568813334794,"status":"error","err-code":"invalid-parameter","err-msg":"invalid symbol"}
                    'symbol trade not open now': BadSymbol,  # {"ts":1576210479343,"status":"error","err-code":"invalid-parameter","err-msg":"symbol trade not open now"},
                    'invalid-address': BadRequest,  # {"status":"error","err-code":"invalid-address","err-msg":"Invalid address.","data":null},
                    'base-currency-chain-error': BadRequest,  # {"status":"error","err-code":"base-currency-chain-error","err-msg":"The current currency chain does not exist","data":null},
                    'dw-insufficient-balance': InsufficientFunds,  # {"status":"error","err-code":"dw-insufficient-balance","err-msg":"Insufficient balance. You can only transfer `12.3456` at most.","data":null}
                },
            },
            'options': {
                'defaultNetwork': 'ERC20',
                'networks': {
                    'ETH': 'erc20',
                    'TRX': 'trc20',
                    'HRC20': 'hrc20',
                    'HECO': 'hrc20',
                    'HT': 'hrc20',
                    'ALGO': 'algo',
                    'OMNI': '',
                },
                # https://github.com/ccxt/ccxt/issues/5376
                'fetchOrdersByStatesMethod': 'private_get_order_orders',  # 'private_get_order_history'  # https://github.com/ccxt/ccxt/pull/5392
                'fetchOpenOrdersMethod': 'fetch_open_orders_v1',  # 'fetch_open_orders_v2'  # https://github.com/ccxt/ccxt/issues/5388
                'createMarketBuyOrderRequiresPrice': True,
                'fetchMarketsMethod': 'publicGetCommonSymbols',
                'fetchBalanceMethod': 'privateGetAccountAccountsIdBalance',
                'createOrderMethod': 'privatePostOrderOrdersPlace',
                'language': 'en-US',
                'broker': {
                    'id': 'AA03022abc',
                },
            },
            'commonCurrencies': {
                # https://github.com/ccxt/ccxt/issues/6081
                # https://github.com/ccxt/ccxt/issues/3365
                # https://github.com/ccxt/ccxt/issues/2873
                'GET': 'Themis',  # conflict with GET(Guaranteed Entrance Token, GET Protocol)
                'GTC': 'Game.com',  # conflict with Gitcoin and Gastrocoin
                'HIT': 'HitChain',
                # https://github.com/ccxt/ccxt/issues/7399
                # https://coinmarketcap.com/currencies/pnetwork/
                # https://coinmarketcap.com/currencies/penta/markets/
                # https://en.cryptonomist.ch/blog/eidoo/the-edo-to-pnt-upgrade-what-you-need-to-know-updated/
                'PNT': 'Penta',
                'SBTC': 'Super Bitcoin',
                'BIFI': 'Bitcoin File',  # conflict with Beefy.Finance https://github.com/ccxt/ccxt/issues/8706
            },
        })

    def fetch_time(self, params={}):
        """
        fetches the current integer timestamp in milliseconds from the exchange server
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns int: the current integer timestamp in milliseconds from the exchange server
        """
        response = self.publicGetCommonTimestamp(params)
        return self.safe_integer(response, 'data')

    def fetch_trading_limits(self, symbols: Strings = None, params={}):
        # self method should not be called directly, use loadTradingLimits() instead
        #  by default it will try load withdrawal fees of all currencies(with separate requests)
        #  however if you define symbols = ['ETH/BTC', 'LTC/BTC'] in args it will only load those
        self.load_markets()
        if symbols is None:
            symbols = self.symbols
        result = {}
        for i in range(0, len(symbols)):
            symbol = symbols[i]
            result[symbol] = self.fetch_trading_limits_by_id(self.market_id(symbol), params)
        return result

    def fetch_trading_limits_by_id(self, id: str, params={}):
        request = {
            'symbol': id,
        }
        response = self.publicGetCommonExchange(self.extend(request, params))
        #
        #     {status:   "ok",
        #         "data": {                                 symbol: "aidocbtc",
        #                              "buy-limit-must-less-than":  1.1,
        #                          "sell-limit-must-greater-than":  0.9,
        #                         "limit-order-must-greater-than":  1,
        #                            "limit-order-must-less-than":  5000000,
        #                    "market-buy-order-must-greater-than":  0.0001,
        #                       "market-buy-order-must-less-than":  100,
        #                   "market-sell-order-must-greater-than":  1,
        #                      "market-sell-order-must-less-than":  500000,
        #                       "circuit-break-when-greater-than":  10000,
        #                          "circuit-break-when-less-than":  10,
        #                 "market-sell-order-rate-must-less-than":  0.1,
        #                  "market-buy-order-rate-must-less-than":  0.1        }}
        #
        return self.parse_trading_limits(self.safe_value(response, 'data', {}))

    def parse_trading_limits(self, limits, symbol: Str = None, params={}):
        #
        #   {                                 symbol: "aidocbtc",
        #                  "buy-limit-must-less-than":  1.1,
        #              "sell-limit-must-greater-than":  0.9,
        #             "limit-order-must-greater-than":  1,
        #                "limit-order-must-less-than":  5000000,
        #        "market-buy-order-must-greater-than":  0.0001,
        #           "market-buy-order-must-less-than":  100,
        #       "market-sell-order-must-greater-than":  1,
        #          "market-sell-order-must-less-than":  500000,
        #           "circuit-break-when-greater-than":  10000,
        #              "circuit-break-when-less-than":  10,
        #     "market-sell-order-rate-must-less-than":  0.1,
        #      "market-buy-order-rate-must-less-than":  0.1        }
        #
        return {
            'info': limits,
            'limits': {
                'amount': {
                    'min': self.safe_number(limits, 'limit-order-must-greater-than'),
                    'max': self.safe_number(limits, 'limit-order-must-less-than'),
                },
            },
        }

    def cost_to_precision(self, symbol, cost):
        return self.decimal_to_precision(cost, TRUNCATE, self.markets[symbol]['precision']['cost'], self.precisionMode)

    def fetch_markets(self, params={}):
        """
        retrieves data on all markets for huobijp
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: an array of objects representing market data
        """
        method = self.options['fetchMarketsMethod']
        response = getattr(self, method)(params)
        #
        #    {
        #        "status": "ok",
        #        "data": [
        #            {
        #                "base-currency": "xrp",
        #                "quote-currency": "btc",
        #                "price-precision": 9,
        #                "amount-precision": 2,
        #                "symbol-partition": "default",
        #                "symbol": "xrpbtc",
        #                "state": "online",
        #                "value-precision": 8,
        #                "min-order-amt": 1,
        #                "max-order-amt": 5000000,
        #                "min-order-value": 0.0001,
        #                "limit-order-min-order-amt": 1,
        #                "limit-order-max-order-amt": 5000000,
        #                "limit-order-max-buy-amt": 5000000,
        #                "limit-order-max-sell-amt": 5000000,
        #                "sell-market-min-order-amt": 1,
        #                "sell-market-max-order-amt": 500000,
        #                "buy-market-max-order-value": 100,
        #                "leverage-ratio": 5,
        #                "super-margin-leverage-ratio": 3,
        #                "api-trading": "enabled",
        #                "tags": ""
        #            }
        #            ...
        #         ]
        #    }
        #
        markets = self.safe_value(response, 'data', [])
        numMarkets = len(markets)
        if numMarkets < 1:
            raise NetworkError(self.id + ' fetchMarkets() returned empty response: ' + self.json(markets))
        result = []
        for i in range(0, len(markets)):
            market = markets[i]
            baseId = self.safe_string(market, 'base-currency')
            quoteId = self.safe_string(market, 'quote-currency')
            base = self.safe_currency_code(baseId)
            quote = self.safe_currency_code(quoteId)
            state = self.safe_string(market, 'state')
            leverageRatio = self.safe_string(market, 'leverage-ratio', '1')
            superLeverageRatio = self.safe_string(market, 'super-margin-leverage-ratio', '1')
            margin = Precise.string_gt(leverageRatio, '1') or Precise.string_gt(superLeverageRatio, '1')
            fee = self.parse_number('0') if (base == 'OMG') else self.parse_number('0.002')
            result.append({
                'id': baseId + quoteId,
                'symbol': base + '/' + quote,
                'base': base,
                'quote': quote,
                'settle': None,
                'baseId': baseId,
                'quoteId': quoteId,
                'settleId': None,
                'type': 'spot',
                'spot': True,
                'margin': margin,
                'swap': False,
                'future': False,
                'option': False,
                'active': (state == 'online'),
                'contract': False,
                'linear': None,
                'inverse': None,
                'taker': fee,
                'maker': fee,
                'contractSize': None,
                'expiry': None,
                'expiryDatetime': None,
                'strike': None,
                'optionType': None,
                'precision': {
                    'price': self.parse_number(self.parse_precision(self.safe_string(market, 'price-precision'))),
                    'amount': self.parse_number(self.parse_precision(self.safe_string(market, 'amount-precision'))),
                    'cost': self.parse_number(self.parse_precision(self.safe_string(market, 'value-precision'))),
                },
                'limits': {
                    'leverage': {
                        'min': self.parse_number('1'),
                        'max': self.parse_number(leverageRatio),
                        'superMax': self.parse_number(superLeverageRatio),
                    },
                    'amount': {
                        'min': self.safe_number(market, 'min-order-amt'),
                        'max': self.safe_number(market, 'max-order-amt'),
                    },
                    'price': {
                        'min': None,
                        'max': None,
                    },
                    'cost': {
                        'min': self.safe_number(market, 'min-order-value'),
                        'max': None,
                    },
                },
                'created': None,
                'info': market,
            })
        return result

    def parse_ticker(self, ticker, market: Market = None) -> Ticker:
        #
        # fetchTicker
        #
        #     {
        #         "amount": 26228.672978342216,
        #         "open": 9078.95,
        #         "close": 9146.86,
        #         "high": 9155.41,
        #         "id": 209988544334,
        #         "count": 265846,
        #         "low": 8988.0,
        #         "version": 209988544334,
        #         "ask": [9146.87, 0.156134],
        #         "vol": 2.3822168242201668E8,
        #         "bid": [9146.86, 0.080758],
        #     }
        #
        # fetchTickers
        #     {
        #         "symbol": "bhdht",
        #         "open":  2.3938,
        #         "high":  2.4151,
        #         "low":  2.3323,
        #         "close":  2.3909,
        #         "amount":  628.992,
        #         "vol":  1493.71841095,
        #         "count":  2088,
        #         "bid":  2.3643,
        #         "bidSize":  0.7136,
        #         "ask":  2.4061,
        #         "askSize":  0.4156
        #     }
        #
        symbol = self.safe_symbol(None, market)
        timestamp = self.safe_integer(ticker, 'ts')
        bid = None
        bidVolume = None
        ask = None
        askVolume = None
        if 'bid' in ticker:
            if isinstance(ticker['bid'], list):
                bid = self.safe_string(ticker['bid'], 0)
                bidVolume = self.safe_string(ticker['bid'], 1)
            else:
                bid = self.safe_string(ticker, 'bid')
                bidVolume = self.safe_string(ticker, 'bidSize')
        if 'ask' in ticker:
            if isinstance(ticker['ask'], list):
                ask = self.safe_string(ticker['ask'], 0)
                askVolume = self.safe_string(ticker['ask'], 1)
            else:
                ask = self.safe_string(ticker, 'ask')
                askVolume = self.safe_string(ticker, 'askSize')
        open = self.safe_string(ticker, 'open')
        close = self.safe_string(ticker, 'close')
        baseVolume = self.safe_string(ticker, 'amount')
        quoteVolume = self.safe_string(ticker, 'vol')
        return self.safe_ticker({
            'symbol': symbol,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'high': self.safe_string(ticker, 'high'),
            'low': self.safe_string(ticker, 'low'),
            'bid': bid,
            'bidVolume': bidVolume,
            'ask': ask,
            'askVolume': askVolume,
            'vwap': None,
            'open': open,
            'close': close,
            'last': close,
            'previousClose': None,
            'change': None,
            'percentage': None,
            'average': None,
            'baseVolume': baseVolume,
            'quoteVolume': quoteVolume,
            'info': ticker,
        }, market)

    def fetch_order_book(self, symbol: str, limit: Int = None, params={}) -> OrderBook:
        """
        fetches information on open orders with bid(buy) and ask(sell) prices, volumes and other data
        :param str symbol: unified symbol of the market to fetch the order book for
        :param int [limit]: the maximum amount of order book entries to return
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: A dictionary of `order book structures <https://docs.ccxt.com/#/?id=order-book-structure>` indexed by market symbols
        """
        self.load_markets()
        market = self.market(symbol)
        request = {
            'symbol': market['id'],
            'type': 'step0',
        }
        response = self.marketGetDepth(self.extend(request, params))
        #
        #     {
        #         "status": "ok",
        #         "ch": "market.btcusdt.depth.step0",
        #         "ts": 1583474832790,
        #         "tick": {
        #             "bids": [
        #                 [9100.290000000000000000, 0.200000000000000000],
        #                 [9099.820000000000000000, 0.200000000000000000],
        #                 [9099.610000000000000000, 0.205000000000000000],
        #             ],
        #             "asks": [
        #                 [9100.640000000000000000, 0.005904000000000000],
        #                 [9101.010000000000000000, 0.287311000000000000],
        #                 [9101.030000000000000000, 0.012121000000000000],
        #             ],
        #             "ts":1583474832008,
        #             "version":104999698780
        #         }
        #     }
        #
        if 'tick' in response:
            if not response['tick']:
                raise BadSymbol(self.id + ' fetchOrderBook() returned empty response: ' + self.json(response))
            tick = self.safe_value(response, 'tick')
            timestamp = self.safe_integer(tick, 'ts', self.safe_integer(response, 'ts'))
            result = self.parse_order_book(tick, symbol, timestamp)
            result['nonce'] = self.safe_integer(tick, 'version')
            return result
        raise ExchangeError(self.id + ' fetchOrderBook() returned unrecognized response: ' + self.json(response))

    def fetch_ticker(self, symbol: str, params={}) -> Ticker:
        """
        fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
        :param str symbol: unified symbol of the market to fetch the ticker for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `ticker structure <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        self.load_markets()
        market = self.market(symbol)
        request = {
            'symbol': market['id'],
        }
        response = self.marketGetDetailMerged(self.extend(request, params))
        #
        #     {
        #         "status": "ok",
        #         "ch": "market.btcusdt.detail.merged",
        #         "ts": 1583494336669,
        #         "tick": {
        #             "amount": 26228.672978342216,
        #             "open": 9078.95,
        #             "close": 9146.86,
        #             "high": 9155.41,
        #             "id": 209988544334,
        #             "count": 265846,
        #             "low": 8988.0,
        #             "version": 209988544334,
        #             "ask": [9146.87, 0.156134],
        #             "vol": 2.3822168242201668E8,
        #             "bid": [9146.86, 0.080758],
        #         }
        #     }
        #
        ticker = self.parse_ticker(response['tick'], market)
        timestamp = self.safe_integer(response, 'ts')
        ticker['timestamp'] = timestamp
        ticker['datetime'] = self.iso8601(timestamp)
        return ticker

    def fetch_tickers(self, symbols: Strings = None, params={}) -> Tickers:
        """
        fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
        :param str[]|None symbols: unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `ticker structures <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        self.load_markets()
        symbols = self.market_symbols(symbols)
        response = self.marketGetTickers(params)
        tickers = self.safe_value(response, 'data', [])
        timestamp = self.safe_integer(response, 'ts')
        result = {}
        for i in range(0, len(tickers)):
            marketId = self.safe_string(tickers[i], 'symbol')
            market = self.safe_market(marketId)
            symbol = market['symbol']
            ticker = self.parse_ticker(tickers[i], market)
            ticker['timestamp'] = timestamp
            ticker['datetime'] = self.iso8601(timestamp)
            result[symbol] = ticker
        return self.filter_by_array_tickers(result, 'symbol', symbols)

    def parse_trade(self, trade, market: Market = None) -> Trade:
        #
        # fetchTrades(public)
        #
        #     {
        #         "amount": 0.010411000000000000,
        #         "trade-id": 102090736910,
        #         "ts": 1583497692182,
        #         "id": 10500517034273194594947,
        #         "price": 9096.050000000000000000,
        #         "direction": "sell"
        #     }
        #
        # fetchMyTrades(private)
        #
        #     {
        #          "symbol": "swftcbtc",
        #          "fee-currency": "swftc",
        #          "filled-fees": "0",
        #          "source": "spot-api",
        #          "id": 83789509854000,
        #          "type": "buy-limit",
        #          "order-id": 83711103204909,
        #          'filled-points': "0.005826843283532154",
        #          "fee-deduct-currency": "ht",
        #          'filled-amount': "45941.53",
        #          "price": "0.0000001401",
        #          "created-at": 1597933260729,
        #          "match-id": 100087455560,
        #          "role": "maker",
        #          "trade-id": 100050305348
        #     },
        #
        marketId = self.safe_string(trade, 'symbol')
        symbol = self.safe_symbol(marketId, market)
        timestamp = self.safe_integer_2(trade, 'ts', 'created-at')
        order = self.safe_string(trade, 'order-id')
        side = self.safe_string(trade, 'direction')
        type = self.safe_string(trade, 'type')
        if type is not None:
            typeParts = type.split('-')
            side = typeParts[0]
            type = typeParts[1]
        takerOrMaker = self.safe_string(trade, 'role')
        price = self.safe_string(trade, 'price')
        amount = self.safe_string_2(trade, 'filled-amount', 'amount')
        cost = Precise.string_mul(price, amount)
        fee = None
        feeCost = self.safe_string(trade, 'filled-fees')
        feeCurrency = self.safe_currency_code(self.safe_string(trade, 'fee-currency'))
        filledPoints = self.safe_string(trade, 'filled-points')
        if filledPoints is not None:
            if (feeCost is None) or (Precise.string_eq(feeCost, '0.0')):
                feeCost = filledPoints
                feeCurrency = self.safe_currency_code(self.safe_string(trade, 'fee-deduct-currency'))
        if feeCost is not None:
            fee = {
                'cost': feeCost,
                'currency': feeCurrency,
            }
        tradeId = self.safe_string_2(trade, 'trade-id', 'tradeId')
        id = self.safe_string(trade, 'id', tradeId)
        return self.safe_trade({
            'info': trade,
            'id': id,
            'symbol': symbol,
            'order': order,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'type': type,
            'side': side,
            'takerOrMaker': takerOrMaker,
            'price': price,
            'amount': amount,
            'cost': cost,
            'fee': fee,
        })

    def fetch_order_trades(self, id: str, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch all the trades made from a single order
        :param str id: order id
        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trades to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        self.load_markets()
        request = {
            'id': id,
        }
        response = self.privateGetOrderOrdersIdMatchresults(self.extend(request, params))
        return self.parse_trades(response['data'], None, since, limit)

    def fetch_my_trades(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch all trades made by the user
        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trades structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        self.load_markets()
        market = None
        request = {}
        if symbol is not None:
            market = self.market(symbol)
            request['symbol'] = market['id']
        if limit is not None:
            request['size'] = limit  # 1-100 orders, default is 100
        if since is not None:
            request['start-time'] = since  # a date within 120 days from today
            # request['end-time'] = self.sum(since, 172800000)  # 48 hours window
        response = self.privateGetOrderMatchresults(self.extend(request, params))
        return self.parse_trades(response['data'], market, since, limit)

    def fetch_trades(self, symbol: str, since: Int = None, limit=1000, params={}) -> List[Trade]:
        """
        get the list of most recent trades for a particular symbol
        :param str symbol: unified symbol of the market to fetch trades for
        :param int [since]: timestamp in ms of the earliest trade to fetch
        :param int [limit]: the maximum amount of trades to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=public-trades>`
        """
        self.load_markets()
        market = self.market(symbol)
        request = {
            'symbol': market['id'],
        }
        if limit is not None:
            request['size'] = min(limit, 2000)
        response = self.marketGetHistoryTrade(self.extend(request, params))
        #
        #     {
        #         "status": "ok",
        #         "ch": "market.btcusdt.trade.detail",
        #         "ts": 1583497692365,
        #         "data": [
        #             {
        #                 "id": 105005170342,
        #                 "ts": 1583497692182,
        #                 "data": [
        #                     {
        #                         "amount": 0.010411000000000000,
        #                         "trade-id": 102090736910,
        #                         "ts": 1583497692182,
        #                         "id": 10500517034273194594947,
        #                         "price": 9096.050000000000000000,
        #                         "direction": "sell"
        #                     }
        #                 ]
        #             },
        #             # ...
        #         ]
        #     }
        #
        data = self.safe_value(response, 'data', [])
        result = []
        for i in range(0, len(data)):
            trades = self.safe_value(data[i], 'data', [])
            for j in range(0, len(trades)):
                trade = self.parse_trade(trades[j], market)
                result.append(trade)
        result = self.sort_by(result, 'timestamp')
        return self.filter_by_symbol_since_limit(result, market['symbol'], since, limit)

    def parse_ohlcv(self, ohlcv, market: Market = None) -> list:
        #
        #     {
        #         "amount":1.2082,
        #         "open":0.025096,
        #         "close":0.025095,
        #         "high":0.025096,
        #         "id":**********,
        #         "count":6,
        #         "low":0.025095,
        #         "vol":0.**********
        #     }
        #
        return [
            self.safe_timestamp(ohlcv, 'id'),
            self.safe_number(ohlcv, 'open'),
            self.safe_number(ohlcv, 'high'),
            self.safe_number(ohlcv, 'low'),
            self.safe_number(ohlcv, 'close'),
            self.safe_number(ohlcv, 'amount'),
        ]

    def fetch_ohlcv(self, symbol: str, timeframe='1m', since: Int = None, limit=1000, params={}) -> List[list]:
        """
        fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
        :param str symbol: unified symbol of the market to fetch OHLCV data for
        :param str timeframe: the length of time each candle represents
        :param int [since]: timestamp in ms of the earliest candle to fetch
        :param int [limit]: the maximum amount of candles to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns int[][]: A list of candles ordered, open, high, low, close, volume
        """
        self.load_markets()
        market = self.market(symbol)
        request = {
            'symbol': market['id'],
            'period': self.safe_string(self.timeframes, timeframe, timeframe),
        }
        if limit is not None:
            request['size'] = limit
        response = self.marketGetHistoryKline(self.extend(request, params))
        #
        #     {
        #         "status":"ok",
        #         "ch":"market.ethbtc.kline.1min",
        #         "ts":1591515374371,
        #         "data":[
        #             {"amount":0.0,"open":0.025095,"close":0.025095,"high":0.025095,"id":1591515360,"count":0,"low":0.025095,"vol":0.0},
        #             {"amount":1.2082,"open":0.025096,"close":0.025095,"high":0.025096,"id":**********,"count":6,"low":0.025095,"vol":0.**********},
        #             {"amount":0.0648,"open":0.025096,"close":0.025096,"high":0.025096,"id":**********,"count":2,"low":0.025096,"vol":0.**********},
        #         ]
        #     }
        #
        data = self.safe_value(response, 'data', [])
        return self.parse_ohlcvs(data, market, timeframe, since, limit)

    def fetch_accounts(self, params={}):
        """
        fetch all the accounts associated with a profile
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `account structures <https://docs.ccxt.com/#/?id=account-structure>` indexed by the account type
        """
        self.load_markets()
        response = self.privateGetAccountAccounts(params)
        return response['data']

    def fetch_currencies(self, params={}):
        """
        fetches all available currencies on an exchange
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an associative dictionary of currencies
        """
        request = {
            'language': self.options['language'],
        }
        response = self.publicGetSettingsCurrencys(self.extend(request, params))
        #
        #     {
        #         "status":"ok",
        #         "data":[
        #             {
        #                 "currency-addr-with-tag":false,
        #                 "fast-confirms":12,
        #                 "safe-confirms":12,
        #                 "currency-type":"eth",
        #                 "quote-currency":true,
        #                 "withdraw-enable-timestamp":*************,
        #                 "deposit-enable-timestamp":*************,
        #                 "currency-partition":"all",
        #                 "support-sites":["OTC","INSTITUTION","MINEPOOL"],
        #                 "withdraw-precision":6,
        #                 "visible-assets-timestamp":1508839200000,
        #                 "deposit-min-amount":"1",
        #                 "withdraw-min-amount":"10",
        #                 "show-precision":"8",
        #                 "tags":"",
        #                 "weight":23,
        #                 "full-name":"Tether USDT",
        #                 "otc-enable":1,
        #                 "visible":true,
        #                 "white-enabled":false,
        #                 "country-disabled":false,
        #                 "deposit-enabled":true,
        #                 "withdraw-enabled":true,
        #                 "name":"usdt",
        #                 "state":"online",
        #                 "display-name":"USDT",
        #                 "suspend-withdraw-desc":null,
        #                 "withdraw-desc":"Minimum withdrawal amount: 10 USDT(ERC20). not >_<not To ensure the safety of your funds, your withdrawal request will be manually reviewed if your security strategy or password is changed. Please wait for phone calls or emails from our staff.not >_<not Please make sure that your computer and browser are secure and your information is protected from being tampered or leaked.",
        #                 "suspend-deposit-desc":null,
        #                 "deposit-desc":"Please don’t deposit any other digital assets except USDT to the above address. Otherwise, you may lose your assets permanently. not >_<not Depositing to the above address requires confirmations of the entire network. It will arrive after 12 confirmations, and it will be available to withdraw after 12 confirmations. not >_<not Minimum deposit amount: 1 USDT. Any deposits less than the minimum will not be credited or refunded.not >_<not Your deposit address won’t change often. If there are any changes, we will notify you via announcement or email.not >_<not Please make sure that your computer and browser are secure and your information is protected from being tampered or leaked.",
        #                 "suspend-visible-desc":null
        #             }
        #         ]
        #     }
        #
        currencies = self.safe_value(response, 'data', [])
        result = {}
        for i in range(0, len(currencies)):
            currency = currencies[i]
            id = self.safe_value(currency, 'name')
            code = self.safe_currency_code(id)
            depositEnabled = self.safe_value(currency, 'deposit-enabled')
            withdrawEnabled = self.safe_value(currency, 'withdraw-enabled')
            countryDisabled = self.safe_value(currency, 'country-disabled')
            visible = self.safe_value(currency, 'visible', False)
            state = self.safe_string(currency, 'state')
            active = visible and depositEnabled and withdrawEnabled and (state == 'online') and not countryDisabled
            name = self.safe_string(currency, 'display-name')
            precision = self.parse_number(self.parse_precision(self.safe_string(currency, 'withdraw-precision')))
            result[code] = {
                'id': id,
                'code': code,
                'type': 'crypto',
                # 'payin': currency['deposit-enabled'],
                # 'payout': currency['withdraw-enabled'],
                # 'transfer': None,
                'name': name,
                'active': active,
                'deposit': depositEnabled,
                'withdraw': withdrawEnabled,
                'fee': None,  # todo need to fetch from fee endpoint
                'precision': precision,
                'limits': {
                    'amount': {
                        'min': precision,
                        'max': None,
                    },
                    'deposit': {
                        'min': self.safe_number(currency, 'deposit-min-amount'),
                        'max': None,
                    },
                    'withdraw': {
                        'min': self.safe_number(currency, 'withdraw-min-amount'),
                        'max': None,
                    },
                },
                'info': currency,
            }
        return result

    def parse_balance(self, response) -> Balances:
        balances = self.safe_value(response['data'], 'list', [])
        result = {'info': response}
        for i in range(0, len(balances)):
            balance = balances[i]
            currencyId = self.safe_string(balance, 'currency')
            code = self.safe_currency_code(currencyId)
            account = None
            if code in result:
                account = result[code]
            else:
                account = self.account()
            if balance['type'] == 'trade':
                account['free'] = self.safe_string(balance, 'balance')
            if balance['type'] == 'frozen':
                account['used'] = self.safe_string(balance, 'balance')
            result[code] = account
        return self.safe_balance(result)

    def fetch_balance(self, params={}) -> Balances:
        """
        query for balance and get the amount of funds available for trading or funds locked in orders
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `balance structure <https://docs.ccxt.com/#/?id=balance-structure>`
        """
        self.load_markets()
        self.load_accounts()
        method = self.options['fetchBalanceMethod']
        request = {
            'id': self.accounts[0]['id'],
        }
        response = getattr(self, method)(self.extend(request, params))
        return self.parse_balance(response)

    def fetch_orders_by_states(self, states, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        self.load_markets()
        request = {
            'states': states,
        }
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['symbol'] = market['id']
        method = self.safe_string(self.options, 'fetchOrdersByStatesMethod', 'private_get_order_orders')
        response = getattr(self, method)(self.extend(request, params))
        #
        #     {"status":   "ok",
        #         "data": [{                 id:  ***********,
        #                                "symbol": "ethbtc",
        #                          "account-id":  3398321,
        #                                "amount": "0.045000000000000000",
        #                                 "price": "0.034014000000000000",
        #                          "created-at":  *************,
        #                                  "type": "sell-limit",
        #                        "field-amount": "0.045000000000000000",
        #                   "field-cash-amount": "0.001530630000000000",
        #                          "field-fees": "0.000003061260000000",
        #                         "finished-at":  *************,
        #                                "source": "spot-api",
        #                                 "state": "filled",
        #                         "canceled-at":  0                      }  ]}
        #
        return self.parse_orders(response['data'], market, since, limit)

    def fetch_order(self, id: str, symbol: Str = None, params={}):
        """
        fetches information on an order made by the user
        :param str symbol: unified symbol of the market the order was made in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        self.load_markets()
        request = {
            'id': id,
        }
        response = self.privateGetOrderOrdersId(self.extend(request, params))
        order = self.safe_value(response, 'data')
        return self.parse_order(order)

    def fetch_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetches information on multiple orders made by the user
        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        return self.fetch_orders_by_states('pre-submitted,submitted,partial-filled,filled,partial-canceled,canceled', symbol, since, limit, params)

    def fetch_open_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetch all unfilled currently open orders
        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch open orders for
        :param int [limit]: the maximum number of  open orders structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        method = self.safe_string(self.options, 'fetchOpenOrdersMethod', 'fetch_open_orders_v1')
        return getattr(self, method)(symbol, since, limit, params)

    def fetch_open_orders_v1(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchOpenOrdersV1() requires a symbol argument')
        return self.fetch_orders_by_states('pre-submitted,submitted,partial-filled', symbol, since, limit, params)

    def fetch_closed_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetches information on multiple closed orders made by the user
        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        return self.fetch_orders_by_states('filled,partial-canceled,canceled', symbol, since, limit, params)

    def fetch_open_orders_v2(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        self.load_markets()
        request = {}
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['symbol'] = market['id']
        accountId = self.safe_string(params, 'account-id')
        if accountId is None:
            # pick the first account
            self.load_accounts()
            for i in range(0, len(self.accounts)):
                account = self.accounts[i]
                if account['type'] == 'spot':
                    accountId = self.safe_string(account, 'id')
                    if accountId is not None:
                        break
        request['account-id'] = accountId
        if limit is not None:
            request['size'] = limit
        omitted = self.omit(params, 'account-id')
        response = self.privateGetOrderOpenOrders(self.extend(request, omitted))
        #
        #     {
        #         "status":"ok",
        #         "data":[
        #             {
        #                 "symbol":"ethusdt",
        #                 "source":"api",
        #                 "amount":"0.010000000000000000",
        #                 "account-id":1528640,
        #                 "created-at":*************,
        #                 "price":"400.000000000000000000",
        #                 "filled-amount":"0.0",
        #                 "filled-cash-amount":"0.0",
        #                 "filled-fees":"0.0",
        #                 "id":***********,
        #                 "state":"submitted",
        #                 "type":"sell-limit"
        #             }
        #         ]
        #     }
        #
        data = self.safe_value(response, 'data', [])
        return self.parse_orders(data, market, since, limit)

    def parse_order_status(self, status):
        statuses = {
            'partial-filled': 'open',
            'partial-canceled': 'canceled',
            'filled': 'closed',
            'canceled': 'canceled',
            'submitted': 'open',
        }
        return self.safe_string(statuses, status, status)

    def parse_order(self, order, market: Market = None) -> Order:
        #
        #     {                 id:  ***********,
        #                    "symbol": "ethbtc",
        #              "account-id":  3398321,
        #                    "amount": "0.045000000000000000",
        #                     "price": "0.034014000000000000",
        #              "created-at":  *************,
        #                      "type": "sell-limit",
        #            "field-amount": "0.045000000000000000",  # they have fixed it for filled-amount
        #       "field-cash-amount": "0.001530630000000000",  # they have fixed it for filled-cash-amount
        #              "field-fees": "0.000003061260000000",  # they have fixed it for filled-fees
        #             "finished-at":  *************,
        #                    "source": "spot-api",
        #                     "state": "filled",
        #             "canceled-at":  0                      }
        #
        #     {                 id:  ***********,
        #                    "symbol": "ethbtc",
        #              "account-id":  5685075,
        #                    "amount": "0.001000000000000000",
        #                     "price": "0.0",
        #              "created-at":  *************,
        #                      "type": "buy-market",
        #            "field-amount": "0.029100000000000000",  # they have fixed it for filled-amount
        #       "field-cash-amount": "0.000999788700000000",  # they have fixed it for filled-cash-amount
        #              "field-fees": "0.000058200000000000",  # they have fixed it for filled-fees
        #             "finished-at":  *************,
        #                    "source": "spot-api",
        #                     "state": "filled",
        #             "canceled-at":  0                      }
        #
        id = self.safe_string(order, 'id')
        side = None
        type = None
        status = None
        if 'type' in order:
            orderType = order['type'].split('-')
            side = orderType[0]
            type = orderType[1]
            status = self.parse_order_status(self.safe_string(order, 'state'))
        marketId = self.safe_string(order, 'symbol')
        market = self.safe_market(marketId, market)
        timestamp = self.safe_integer(order, 'created-at')
        clientOrderId = self.safe_string(order, 'client-order-id')
        amount = self.safe_string(order, 'amount')
        filled = self.safe_string_2(order, 'filled-amount', 'field-amount')  # typo in their API, filled amount
        price = self.safe_string(order, 'price')
        cost = self.safe_string_2(order, 'filled-cash-amount', 'field-cash-amount')  # same typo
        feeCost = self.safe_string_2(order, 'filled-fees', 'field-fees')  # typo in their API, filled fees
        fee = None
        if feeCost is not None:
            feeCurrency = market['quote'] if (side == 'sell') else market['base']
            fee = {
                'cost': feeCost,
                'currency': feeCurrency,
            }
        return self.safe_order({
            'info': order,
            'id': id,
            'clientOrderId': clientOrderId,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'lastTradeTimestamp': None,
            'symbol': market['symbol'],
            'type': type,
            'timeInForce': None,
            'postOnly': None,
            'side': side,
            'price': price,
            'stopPrice': None,
            'triggerPrice': None,
            'average': None,
            'cost': cost,
            'amount': amount,
            'filled': filled,
            'remaining': None,
            'status': status,
            'fee': fee,
            'trades': None,
        }, market)

    def create_market_buy_order_with_cost(self, symbol: str, cost, params={}):
        """
        create a market buy order by providing the symbol and cost
        :param str symbol: unified symbol of the market to create an order in
        :param float cost: how much you want to trade in units of the quote currency
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        self.load_markets()
        market = self.market(symbol)
        if not market['spot']:
            raise NotSupported(self.id + ' createMarketBuyOrderWithCost() supports spot orders only')
        params['createMarketBuyOrderRequiresPrice'] = False
        return self.create_order(symbol, 'market', 'buy', cost, None, params)

    def create_order(self, symbol: str, type: OrderType, side: OrderSide, amount, price=None, params={}):
        """
        create a trade order
        :param str symbol: unified symbol of the market to create an order in
        :param str type: 'market' or 'limit'
        :param str side: 'buy' or 'sell'
        :param float amount: how much of currency you want to trade in units of base currency
        :param float [price]: the price at which the order is to be fullfilled, in units of the quote currency, ignored in market orders
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        self.load_markets()
        self.load_accounts()
        market = self.market(symbol)
        request = {
            'account-id': self.accounts[0]['id'],
            'symbol': market['id'],
            'type': side + '-' + type,
        }
        clientOrderId = self.safe_string_2(params, 'clientOrderId', 'client-order-id')  # must be 64 chars max and unique within 24 hours
        if clientOrderId is None:
            broker = self.safe_value(self.options, 'broker', {})
            brokerId = self.safe_string(broker, 'id')
            request['client-order-id'] = brokerId + self.uuid()
        else:
            request['client-order-id'] = clientOrderId
        params = self.omit(params, ['clientOrderId', 'client-order-id'])
        if (type == 'market') and (side == 'buy'):
            quoteAmount = None
            createMarketBuyOrderRequiresPrice = True
            createMarketBuyOrderRequiresPrice, params = self.handle_option_and_params(params, 'createOrder', 'createMarketBuyOrderRequiresPrice', True)
            cost = self.safe_number(params, 'cost')
            params = self.omit(params, 'cost')
            if cost is not None:
                quoteAmount = self.amount_to_precision(symbol, cost)
            elif createMarketBuyOrderRequiresPrice:
                if price is None:
                    raise InvalidOrder(self.id + ' createOrder() requires the price argument for market buy orders to calculate the total cost to spend(amount * price), alternatively set the createMarketBuyOrderRequiresPrice option or param to False and pass the cost to spend in the amount argument')
                else:
                    # despite that cost = amount * price is in quote currency and should have quote precision
                    # the exchange API requires the cost supplied in 'amount' to be of base precision
                    # more about it here:
                    # https://github.com/ccxt/ccxt/pull/4395
                    # https://github.com/ccxt/ccxt/issues/7611
                    # we use amountToPrecision here because the exchange requires cost in base precision
                    amountString = self.number_to_string(amount)
                    priceString = self.number_to_string(price)
                    quoteAmount = self.amount_to_precision(symbol, Precise.string_mul(amountString, priceString))
            else:
                quoteAmount = self.amount_to_precision(symbol, amount)
            request['amount'] = quoteAmount
        else:
            request['amount'] = self.amount_to_precision(symbol, amount)
        if type == 'limit' or type == 'ioc' or type == 'limit-maker' or type == 'stop-limit' or type == 'stop-limit-fok':
            request['price'] = self.price_to_precision(symbol, price)
        method = self.options['createOrderMethod']
        response = getattr(self, method)(self.extend(request, params))
        id = self.safe_string(response, 'data')
        return self.safe_order({
            'info': response,
            'id': id,
            'timestamp': None,
            'datetime': None,
            'lastTradeTimestamp': None,
            'status': None,
            'symbol': symbol,
            'type': type,
            'side': side,
            'price': price,
            'amount': amount,
            'filled': None,
            'remaining': None,
            'cost': None,
            'trades': None,
            'fee': None,
            'clientOrderId': None,
            'average': None,
        }, market)

    def cancel_order(self, id: str, symbol: Str = None, params={}):
        """
        cancels an open order
        :param str id: order id
        :param str symbol: not used by huobijp cancelOrder()
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        response = self.privatePostOrderOrdersIdSubmitcancel({'id': id})
        #
        #     {
        #         "status": "ok",
        #         "data": "10138899000",
        #     }
        #
        return self.extend(self.parse_order(response), {
            'id': id,
            'status': 'canceled',
        })

    def cancel_orders(self, ids, symbol: Str = None, params={}):
        """
        cancel multiple orders
        :param str[] ids: order ids
        :param str symbol: not used by huobijp cancelOrders()
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        self.load_markets()
        clientOrderIds = self.safe_value_2(params, 'clientOrderIds', 'client-order-ids')
        params = self.omit(params, ['clientOrderIds', 'client-order-ids'])
        request = {}
        if clientOrderIds is None:
            request['order-ids'] = ids
        else:
            request['client-order-ids'] = clientOrderIds
        response = self.privatePostOrderOrdersBatchcancel(self.extend(request, params))
        #
        #     {
        #         "status": "ok",
        #         "data": {
        #             "success": [
        #                 "5983466"
        #             ],
        #             "failed": [
        #                 {
        #                     "err-msg": "Incorrect order state",
        #                     "order-state": 7,
        #                     "order-id": "",
        #                     "err-code": "order-orderstate-error",
        #                     "client-order-id": "first"
        #                 },
        #                 {
        #                     "err-msg": "Incorrect order state",
        #                     "order-state": 7,
        #                     "order-id": "",
        #                     "err-code": "order-orderstate-error",
        #                     "client-order-id": "second"
        #                 },
        #                 {
        #                     "err-msg": "The record is not found.",
        #                     "order-id": "",
        #                     "err-code": "base-not-found",
        #                     "client-order-id": "third"
        #                 }
        #             ]
        #         }
        #     }
        #
        return response

    def cancel_all_orders(self, symbol: Str = None, params={}):
        """
        cancel all open orders
        :param str symbol: unified market symbol, only orders in the market of self symbol are cancelled when symbol is not None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        self.load_markets()
        request = {
            # 'account-id' string False NA The account id used for self cancel Refer to GET /v1/account/accounts
            # 'symbol': market['id'],  # a list of comma-separated symbols, all symbols by default
            # 'types' 'string', buy-market, sell-market, buy-limit, sell-limit, buy-ioc, sell-ioc, buy-stop-limit, sell-stop-limit, buy-limit-fok, sell-limit-fok, buy-stop-limit-fok, sell-stop-limit-fok
            # 'side': 'buy',  # or 'sell'
            # 'size': 100,  # the number of orders to cancel 1-100
        }
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['symbol'] = market['id']
        response = self.privatePostOrderOrdersBatchCancelOpenOrders(self.extend(request, params))
        #
        #     {
        #         "code": 200,
        #         "data": {
        #             "success-count": 2,
        #             "failed-count": 0,
        #             "next-id": 5454600
        #         }
        #     }
        #
        return response

    def currency_to_precision(self, code, fee, networkCode=None):
        return self.decimal_to_precision(fee, 0, self.currencies[code]['precision'], self.precisionMode)

    def safe_network(self, networkId):
        lastCharacterIndex = len(networkId) - 1
        lastCharacter = networkId[lastCharacterIndex]
        if lastCharacter == '1':
            networkId = networkId[0:lastCharacterIndex]
        networksById = {}
        return self.safe_string(networksById, networkId, networkId)

    def parse_deposit_address(self, depositAddress, currency: Currency = None):
        #
        #     {
        #         "currency": "usdt",
        #         "address": "0xf7292eb9ba7bc50358e27f0e025a4d225a64127b",
        #         "addressTag": "",
        #         "chain": "usdterc20",  # trc20usdt, hrc20usdt, usdt, algousdt
        #     }
        #
        address = self.safe_string(depositAddress, 'address')
        tag = self.safe_string(depositAddress, 'addressTag')
        currencyId = self.safe_string(depositAddress, 'currency')
        currency = self.safe_currency(currencyId, currency)
        code = self.safe_currency_code(currencyId, currency)
        networkId = self.safe_string(depositAddress, 'chain')
        networks = self.safe_value(currency, 'networks', {})
        networksById = self.index_by(networks, 'id')
        networkValue = self.safe_value(networksById, networkId, networkId)
        network = self.safe_string(networkValue, 'network')
        self.check_address(address)
        return {
            'currency': code,
            'address': address,
            'tag': tag,
            'network': network,
            'info': depositAddress,
        }

    def fetch_deposits(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Transaction]:
        """
        fetch all deposits made to an account
        :param str code: unified currency code
        :param int [since]: the earliest time in ms to fetch deposits for
        :param int [limit]: the maximum number of deposits structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        if limit is None or limit > 100:
            limit = 100
        self.load_markets()
        currency = None
        if code is not None:
            currency = self.currency(code)
        request = {
            'type': 'deposit',
            'from': 0,  # From 'id' ... if you want to get results after a particular transaction id, pass the id in params.from
        }
        if currency is not None:
            request['currency'] = currency['id']
        if limit is not None:
            request['size'] = limit  # max 100
        response = self.privateGetQueryDepositWithdraw(self.extend(request, params))
        # return response
        return self.parse_transactions(response['data'], currency, since, limit)

    def fetch_withdrawals(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Transaction]:
        """
        fetch all withdrawals made from an account
        :param str code: unified currency code
        :param int [since]: the earliest time in ms to fetch withdrawals for
        :param int [limit]: the maximum number of withdrawals structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        if limit is None or limit > 100:
            limit = 100
        self.load_markets()
        currency = None
        if code is not None:
            currency = self.currency(code)
        request = {
            'type': 'withdraw',
            'from': 0,  # From 'id' ... if you want to get results after a particular transaction id, pass the id in params.from
        }
        if currency is not None:
            request['currency'] = currency['id']
        if limit is not None:
            request['size'] = limit  # max 100
        response = self.privateGetQueryDepositWithdraw(self.extend(request, params))
        # return response
        return self.parse_transactions(response['data'], currency, since, limit)

    def parse_transaction(self, transaction, currency: Currency = None) -> Transaction:
        #
        # fetchDeposits
        #
        #     {
        #         "id": 8211029,
        #         "type": "deposit",
        #         "currency": "eth",
        #         "chain": "eth",
        #         'tx-hash': "bd315....",
        #         "amount": 0.81162421,
        #         "address": "4b8b....",
        #         'address-tag": '",
        #         "fee": 0,
        #         "state": "safe",
        #         "created-at": 1542180380965,
        #         "updated-at": 1542180788077
        #     }
        #
        # fetchWithdrawals
        #
        #     {
        #         "id": 6908275,
        #         "type": "withdraw",
        #         "currency": "btc",
        #         "chain": "btc",
        #         'tx-hash': "c1a1a....",
        #         "amount": 0.80257005,
        #         "address": "1QR....",
        #         'address-tag": '",
        #         "fee": 0.0005,
        #         "state": "confirmed",
        #         "created-at": 1552107295685,
        #         "updated-at": 1552108032859
        #     }
        #
        # withdraw
        #
        #     {
        #         "status": "ok",
        #         "data": "99562054"
        #     }
        #
        timestamp = self.safe_integer(transaction, 'created-at')
        code = self.safe_currency_code(self.safe_string(transaction, 'currency'))
        type = self.safe_string(transaction, 'type')
        if type == 'withdraw':
            type = 'withdrawal'
        feeCost = self.safe_string(transaction, 'fee')
        if feeCost is not None:
            feeCost = Precise.string_abs(feeCost)
        return {
            'info': transaction,
            'id': self.safe_string_2(transaction, 'id', 'data'),
            'txid': self.safe_string(transaction, 'tx-hash'),
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'network': self.safe_string_upper(transaction, 'chain'),
            'address': self.safe_string(transaction, 'address'),
            'addressTo': None,
            'addressFrom': None,
            'tag': self.safe_string(transaction, 'address-tag'),
            'tagTo': None,
            'tagFrom': None,
            'type': type,
            'amount': self.safe_number(transaction, 'amount'),
            'currency': code,
            'status': self.parse_transaction_status(self.safe_string(transaction, 'state')),
            'updated': self.safe_integer(transaction, 'updated-at'),
            'comment': None,
            'internal': None,
            'fee': {
                'currency': code,
                'cost': self.parse_number(feeCost),
                'rate': None,
            },
        }

    def parse_transaction_status(self, status):
        statuses = {
            # deposit statuses
            'unknown': 'failed',
            'confirming': 'pending',
            'confirmed': 'ok',
            'safe': 'ok',
            'orphan': 'failed',
            # withdrawal statuses
            'submitted': 'pending',
            'canceled': 'canceled',
            'reexamine': 'pending',
            'reject': 'failed',
            'pass': 'pending',
            'wallet-reject': 'failed',
            # 'confirmed': 'ok',  # present in deposit statuses
            'confirm-error': 'failed',
            'repealed': 'failed',
            'wallet-transfer': 'pending',
            'pre-transfer': 'pending',
        }
        return self.safe_string(statuses, status, status)

    def withdraw(self, code: str, amount, address, tag=None, params={}):
        """
        make a withdrawal
        :param str code: unified currency code
        :param float amount: the amount to withdraw
        :param str address: the address to withdraw to
        :param str tag:
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `transaction structure <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        tag, params = self.handle_withdraw_tag_and_params(tag, params)
        self.load_markets()
        self.check_address(address)
        currency = self.currency(code)
        request = {
            'address': address,  # only supports existing addresses in your withdraw address list
            'amount': amount,
            'currency': currency['id'].lower(),
        }
        if tag is not None:
            request['addr-tag'] = tag  # only for XRP?
        networks = self.safe_value(self.options, 'networks', {})
        network = self.safe_string_upper(params, 'network')  # self line allows the user to specify either ERC20 or ETH
        network = self.safe_string_lower(networks, network, network)  # handle ETH>ERC20 alias
        if network is not None:
            # possible chains - usdterc20, trc20usdt, hrc20usdt, usdt, algousdt
            if network == 'erc20':
                request['chain'] = currency['id'] + network
            else:
                request['chain'] = network + currency['id']
            params = self.omit(params, 'network')
        response = self.privatePostDwWithdrawApiCreate(self.extend(request, params))
        #
        #     {
        #         "status": "ok",
        #         "data": "99562054"
        #     }
        #
        return self.parse_transaction(response, currency)

    def sign(self, path, api='public', method='GET', params={}, headers=None, body=None):
        url = '/'
        if api == 'market':
            url += api
        elif (api == 'public') or (api == 'private'):
            url += self.version
        elif (api == 'v2Public') or (api == 'v2Private'):
            url += 'v2'
        url += '/' + self.implode_params(path, params)
        query = self.omit(params, self.extract_params(path))
        if api == 'private' or api == 'v2Private':
            self.check_required_credentials()
            timestamp = self.ymdhms(self.milliseconds(), 'T')
            request = {
                'SignatureMethod': 'HmacSHA256',
                'SignatureVersion': '2',
                'AccessKeyId': self.apiKey,
                'Timestamp': timestamp,
            }
            if method != 'POST':
                request = self.extend(request, query)
            requestSorted = self.keysort(request)
            auth = self.urlencode(requestSorted)
            # unfortunately, PHP demands double quotes for the escaped newline symbol
            # eslint-disable-next-line quotes
            payload = "\n".join([method, self.hostname, url, auth])
            signature = self.hmac(self.encode(payload), self.encode(self.secret), hashlib.sha256, 'base64')
            auth += '&' + self.urlencode({'Signature': signature})
            url += '?' + auth
            if method == 'POST':
                body = self.json(query)
                headers = {
                    'Content-Type': 'application/json',
                }
            else:
                headers = {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
        else:
            if params:
                url += '?' + self.urlencode(params)
        url = self.implode_params(self.urls['api'][api], {
            'hostname': self.hostname,
        }) + url
        return {'url': url, 'method': method, 'body': body, 'headers': headers}

    def handle_errors(self, httpCode, reason, url, method, headers, body, response, requestHeaders, requestBody):
        if response is None:
            return None  # fallback to default error handler
        if 'status' in response:
            #
            #     {"status":"error","err-code":"order-limitorder-amount-min-error","err-msg":"limit order amount error, min: `0.001`","data":null}
            #
            status = self.safe_string(response, 'status')
            if status == 'error':
                code = self.safe_string(response, 'err-code')
                feedback = self.id + ' ' + body
                self.throw_broadly_matched_exception(self.exceptions['broad'], body, feedback)
                self.throw_exactly_matched_exception(self.exceptions['exact'], code, feedback)
                message = self.safe_string(response, 'err-msg')
                self.throw_exactly_matched_exception(self.exceptions['exact'], message, feedback)
                raise ExchangeError(feedback)
        return None
