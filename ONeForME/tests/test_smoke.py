import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from decimal import Decimal
from core.exchange_manager import ExchangeManager
from core.strategy_manager import StrategyManager
from core.risk_manager import RiskManager
from config.settings import settings


@pytest.mark.smoke
@pytest.mark.asyncio
async def test_exchange_initialization():
    """Test of exchanges correct worden geïnitialiseerd"""
    exchange_manager = ExchangeManager()

    # Mock de exchange initialisatie om externe dependencies te vermijden
    with patch.object(exchange_manager, "initialize_exchanges") as mock_init:
        mock_init.return_value = None
        exchange_manager.exchanges = {"binance": MagicMock(), "coinbase": MagicMock()}

        await exchange_manager.initialize_exchanges()
        assert len(exchange_manager.exchanges) > 0


@pytest.mark.smoke
@pytest.mark.asyncio
async def test_strategy_initialization():
    """Test van strategie initialisatie"""
    exchange_manager = ExchangeManager()
    strategy_manager = StrategyManager(exchange_manager)

    # Mock de strategy initialisatie
    with patch.object(strategy_manager, "initialize_strategies") as mock_init:
        mock_init.return_value = None
        strategy_manager.strategies = [Magic<PERSON>ock(), MagicMock()]

        await strategy_manager.initialize_strategies()
        assert len(strategy_manager.strategies) > 0


@pytest.mark.smoke
@pytest.mark.asyncio
async def test_risk_calculation():
    """Test van risico berekening"""
    exchange_manager = ExchangeManager()
    risk_manager = RiskManager(exchange_manager)

    # Mock de position size berekening
    with patch.object(risk_manager, "calculate_position_size") as mock_calc:
        mock_calc.return_value = Decimal("0.1")

        result = await risk_manager.calculate_position_size(
            "binance", "BTC/USDT", Decimal("100")
        )
        assert result is not None
        assert isinstance(result, Decimal)


@pytest.mark.smoke
@pytest.mark.asyncio
async def test_bot_startup_components():
    """Test van bot component initialisatie zonder daadwerkelijk de bot te starten"""
    # Test dat alle componenten kunnen worden geïmporteerd
    from main import main
    from bot.handlers import set_managers

    # Test dat managers kunnen worden aangemaakt
    exchange_manager = ExchangeManager()
    strategy_manager = StrategyManager(exchange_manager)
    risk_manager = RiskManager(exchange_manager)

    # Test dat set_managers werkt
    set_managers(exchange_manager, strategy_manager, risk_manager)

    # Test is succesvol als er geen exceptions zijn
    assert True


@pytest.mark.smoke
@pytest.mark.asyncio
async def test_settings_accessibility():
    """Test dat alle benodigde settings toegankelijk zijn"""
    # Test dat belangrijke settings bestaan
    assert hasattr(settings, "TELEGRAM_BOT_TOKEN")
    assert hasattr(settings, "RISK_PERCENT_PER_TRADE")

    # Test dat settings waarden hebben (kunnen None zijn in test omgeving)
    assert settings.RISK_PERCENT_PER_TRADE is not None


@pytest.mark.smoke
def test_imports():
    """Test dat alle belangrijke modules kunnen worden geïmporteerd"""
    # Test core imports
    from core.exchange_manager import ExchangeManager
    from core.strategy_manager import StrategyManager
    from core.risk_manager import RiskManager

    # Test bot imports
    from bot.handlers import (
        start_handler,
        status_handler,
        help_handler,
        monitoring_handler,
        unknown_handler,
        error_handler,
    )

    # Test config imports
    from config.settings import settings

    # Test main import
    import main

    # Als we hier komen, zijn alle imports succesvol
    assert True
