from pydantic_settings import BaseSettings, SettingsConfigDict
from loguru import logger
import os


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    # Telegram Configuration
    TELEGRAM_BOT_TOKEN: str = ""
    ADMIN_USER_IDS: str = ""
    VIEWER_USER_IDS: str = ""
    MONITORING_CHANNEL: str = ""

    # Exchange API Keys
    KUCOIN_API_KEY: str = ""
    KUCOIN_SECRET: str = ""
    KUCOIN_PASSWORD: str = ""
    BINANCE_API_KEY: str = ""
    BINANCE_SECRET: str = ""
    BYBIT_API_KEY: str = ""
    BYBIT_SECRET: str = ""

    # Trading Configuration
    TRADING_PAIRS: str = "BTC/USDT,ETH/USDT"
    TIMEFRAME: str = "5m"
    PAPER_TRADING: str = "true"

    # Risk Management
    RISK_PERCENT_PER_TRADE: float = 2.0
    MAX_CONCURRENT_POSITIONS: int = 3
    MAX_DRAWDOWN_PERCENT: float = 10.0
    POSITION_SIZE_TYPE: str = "percentage"

    # Strategy Parameters
    RSI_PERIOD: int = 14
    MACD_FAST: int = 12
    MACD_SLOW: int = 26
    MACD_SIGNAL: int = 9

    # Database
    DATABASE_URL: str = "sqlite+aiosqlite:///./trading_bot.db"

    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/trading_bot.log"


# Instantiate settings
settings = Settings()

# Logging configuration
logger.remove(0)  # Remove default handler
log_file = os.path.join(os.path.dirname(__file__), "../logs/trading_bot.log")
logger.add(log_file, rotation="10 MB", compression="zip", level="INFO")
logger.add(lambda msg: print(msg, end=""), level="DEBUG")  # Console output
