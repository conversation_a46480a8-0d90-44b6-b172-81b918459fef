from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes, CommandHandler, MessageHandler, filters
from config.settings import settings
from loguru import logger

# Global managers - worden geïnitialiseerd vanuit main.py
exchange_manager = None
strategy_manager = None
risk_manager = None


def set_managers(em, sm, rm):
    """Stel de managers in vanuit main.py"""
    global exchange_manager, strategy_manager, risk_manager
    exchange_manager = em
    strategy_manager = sm
    risk_manager = rm


async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Verwerkt het /start commando"""
    logger.info("Gebruiker heeft /start opgeroepen")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    keyboard = [
        [
            InlineKeyboardButton("📊 Status", callback_data="status"),
            InlineKeyboardButton("⚙️ Instellingen", callback_data="settings"),
        ],
        [InlineKeyboardButton("📈 Monitoring", callback_data="monitoring")],
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    await context.bot.send_message(
        chat_id=update.effective_chat.id,
        text="Trading bot is gestart!\nSelecteer een optie:",
        reply_markup=reply_markup,
    )


async def status_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Toont de huidige status van de bot"""
    logger.info("Gebruiker heeft /status opgeroepen")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    # Gebruik de managers om status te verkrijgen
    status_text = "Bot is actief en functioneert correct.\n\n"

    if exchange_manager:
        status_text += "📊 Exchange Status:\n"
        # Hier kun je later exchange status toevoegen
        status_text += "- Exchanges verbonden\n"

    if strategy_manager:
        status_text += "\n🎯 Strategy Status:\n"
        # Hier kun je later strategy status toevoegen
        status_text += "- Strategieën geladen\n"

    if risk_manager:
        status_text += "\n⚠️ Risk Management:\n"
        # Hier kun je later risk status toevoegen
        status_text += f"- Risk per trade: {settings.RISK_PERCENT_PER_TRADE}%\n"

    await context.bot.send_message(chat_id=update.effective_chat.id, text=status_text)


async def monitoring_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Toont real-time monitoring data"""
    logger.info("Gebruiker heeft monitoring opgeroepen")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    await context.bot.send_message(
        chat_id=update.effective_chat.id, text="Real-time monitoring is actief."
    )


async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Toont hulp informatie"""
    logger.info("Gebruiker heeft /help opgeroepen")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    help_text = "🤖 **TRADING BOT COMMANDO'S**\n\n"
    help_text += "**Basis commando's:**\n"
    help_text += "/start - Start de bot interface\n"
    help_text += "/status - Toon bot en manager status\n"
    help_text += "/help - Toon deze hulp\n"
    help_text += "/monitoring - Real-time monitoring\n\n"

    # Admin commando's alleen tonen aan admins
    user_id = update.effective_user.id if update.effective_user else 0
    if user_id in settings.admin_user_ids_list:
        help_text += "**Admin commando's:**\n"
        help_text += "/safety - Veiligheidscheck voor live trading\n"
        help_text += "/emergency - 🚨 STOP alle trading onmiddellijk\n\n"
        help_text += "⚠️ **Live trading status:**\n"
        help_text += f"Paper trading: {'Aan' if settings.PAPER_TRADING else 'Uit'}\n"
        help_text += (
            f"Live trading: {'Aan' if settings.LIVE_TRADING_ENABLED else 'Uit'}\n"
        )

    await context.bot.send_message(
        chat_id=update.effective_chat.id, text=help_text, parse_mode="Markdown"
    )


async def safety_check_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Voert veiligheidscheck uit voor live trading"""
    logger.info("Gebruiker heeft veiligheidscheck opgeroepen")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    # Controleer of gebruiker admin is
    user_id = update.effective_user.id if update.effective_user else 0
    if user_id not in settings.admin_user_ids_list:
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="❌ Alleen admins kunnen veiligheidscheck uitvoeren.",
        )
        return

    is_safe, errors = settings.is_live_trading_safe()

    if is_safe:
        status_text = "✅ **VEILIGHEIDSCHECK GESLAAGD**\n\n"
        status_text += "Live trading kan veilig worden gestart.\n\n"
        status_text += "**Huidige instellingen:**\n"
        status_text += f"• Risk per trade: {settings.RISK_PERCENT_PER_TRADE}%\n"
        status_text += f"• Max positions: {settings.MAX_CONCURRENT_POSITIONS}\n"
        status_text += f"• Max drawdown: {settings.MAX_DRAWDOWN_PERCENT}%\n"
        status_text += (
            f"• Paper trading: {'Aan' if settings.PAPER_TRADING else 'Uit'}\n"
        )
        status_text += (
            f"• Live trading: {'Aan' if settings.LIVE_TRADING_ENABLED else 'Uit'}\n"
        )
    else:
        status_text = "🚨 **VEILIGHEIDSCHECK GEFAALD**\n\n"
        status_text += "Live trading is NIET veilig om te starten!\n\n"
        status_text += "**Problemen gevonden:**\n"
        for i, error in enumerate(errors, 1):
            status_text += f"{i}. {error}\n"
        status_text += "\n❌ Los deze problemen op voordat je live trading start!"

    await context.bot.send_message(
        chat_id=update.effective_chat.id, text=status_text, parse_mode="Markdown"
    )


async def emergency_stop_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Emergency stop - stopt alle trading onmiddellijk"""
    logger.warning("EMERGENCY STOP opgeroepen!")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    # Controleer of gebruiker admin is
    user_id = update.effective_user.id if update.effective_user else 0
    if user_id not in settings.admin_user_ids_list:
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="❌ Alleen admins kunnen emergency stop activeren.",
        )
        return

    # Hier zou je alle open posities sluiten en trading stoppen
    # Voor nu alleen een waarschuwing

    await context.bot.send_message(
        chat_id=update.effective_chat.id,
        text="🚨 **EMERGENCY STOP GEACTIVEERD** 🚨\n\n"
        "Alle trading is gestopt!\n"
        "Alle open posities worden gesloten.\n\n"
        "⚠️ Herstart de bot om trading te hervatten.",
        parse_mode="Markdown",
    )


async def unknown_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Verwerkt onbekende commando's"""
    logger.warning("Onbekend commando ontvangen")

    if not update.effective_chat:
        logger.error("Geen effective_chat gevonden in update")
        return

    await context.bot.send_message(
        chat_id=update.effective_chat.id,
        text="Onbekend commando. Gebruik /help voor meer informatie.",
    )


async def error_handler(_update: object, context: ContextTypes.DEFAULT_TYPE):
    """Foutafhandeling"""
    logger.error(f"Fout opgetreden: {context.error}")
    # Update parameter wordt niet gebruikt omdat het een object type is voor error handling


# Handler objecten voor registratie
start_handler = CommandHandler("start", start_command)
status_handler = CommandHandler("status", status_command)
help_handler = CommandHandler("help", help_command)
monitoring_handler = CommandHandler("monitoring", monitoring_command)
safety_check_handler = CommandHandler("safety", safety_check_command)
emergency_stop_handler = CommandHandler("emergency", emergency_stop_command)
unknown_handler = MessageHandler(filters.COMMAND, unknown_command)
