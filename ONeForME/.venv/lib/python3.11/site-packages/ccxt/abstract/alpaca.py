from ccxt.base.types import Entry


class ImplicitAPI:
    trader_private_get_v2_account = traderPrivateGetV2Account = Entry('v2/account', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_orders = traderPrivateGetV2Orders = Entry('v2/orders', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_orders_order_id = traderPrivateGetV2OrdersOrderId = Entry('v2/orders/{order_id}', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_positions = traderPrivateGetV2Positions = Entry('v2/positions', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_positions_symbol_or_asset_id = traderPrivateGetV2PositionsSymbolOrAssetId = Entry('v2/positions/{symbol_or_asset_id}', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_account_portfolio_history = traderPrivateGetV2AccountPortfolioHistory = Entry('v2/account/portfolio/history', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_watchlists = traderPrivateGetV2Watchlists = Entry('v2/watchlists', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_watchlists_watchlist_id = traderPrivateGetV2WatchlistsWatchlistId = Entry('v2/watchlists/{watchlist_id}', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_watchlists_by_name = traderPrivateGetV2WatchlistsByName = Entry('v2/watchlists:by_name', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_account_configurations = traderPrivateGetV2AccountConfigurations = Entry('v2/account/configurations', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_account_activities = traderPrivateGetV2AccountActivities = Entry('v2/account/activities', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_account_activities_activity_type = traderPrivateGetV2AccountActivitiesActivityType = Entry('v2/account/activities/{activity_type}', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_calendar = traderPrivateGetV2Calendar = Entry('v2/calendar', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_clock = traderPrivateGetV2Clock = Entry('v2/clock', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_assets = traderPrivateGetV2Assets = Entry('v2/assets', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_assets_symbol_or_asset_id = traderPrivateGetV2AssetsSymbolOrAssetId = Entry('v2/assets/{symbol_or_asset_id}', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_corporate_actions_announcements_id = traderPrivateGetV2CorporateActionsAnnouncementsId = Entry('v2/corporate_actions/announcements/{id}', ['trader', 'private'], 'GET', {})
    trader_private_get_v2_corporate_actions_announcements = traderPrivateGetV2CorporateActionsAnnouncements = Entry('v2/corporate_actions/announcements', ['trader', 'private'], 'GET', {})
    trader_private_post_v2_orders = traderPrivatePostV2Orders = Entry('v2/orders', ['trader', 'private'], 'POST', {})
    trader_private_post_v2_watchlists = traderPrivatePostV2Watchlists = Entry('v2/watchlists', ['trader', 'private'], 'POST', {})
    trader_private_post_v2_watchlists_watchlist_id = traderPrivatePostV2WatchlistsWatchlistId = Entry('v2/watchlists/{watchlist_id}', ['trader', 'private'], 'POST', {})
    trader_private_post_v2_watchlists_by_name = traderPrivatePostV2WatchlistsByName = Entry('v2/watchlists:by_name', ['trader', 'private'], 'POST', {})
    trader_private_put_v2_watchlists_watchlist_id = traderPrivatePutV2WatchlistsWatchlistId = Entry('v2/watchlists/{watchlist_id}', ['trader', 'private'], 'PUT', {})
    trader_private_put_v2_watchlists_by_name = traderPrivatePutV2WatchlistsByName = Entry('v2/watchlists:by_name', ['trader', 'private'], 'PUT', {})
    trader_private_patch_v2_orders_order_id = traderPrivatePatchV2OrdersOrderId = Entry('v2/orders/{order_id}', ['trader', 'private'], 'PATCH', {})
    trader_private_patch_v2_account_configurations = traderPrivatePatchV2AccountConfigurations = Entry('v2/account/configurations', ['trader', 'private'], 'PATCH', {})
    trader_private_delete_v2_orders = traderPrivateDeleteV2Orders = Entry('v2/orders', ['trader', 'private'], 'DELETE', {})
    trader_private_delete_v2_orders_order_id = traderPrivateDeleteV2OrdersOrderId = Entry('v2/orders/{order_id}', ['trader', 'private'], 'DELETE', {})
    trader_private_delete_v2_positions = traderPrivateDeleteV2Positions = Entry('v2/positions', ['trader', 'private'], 'DELETE', {})
    trader_private_delete_v2_positions_symbol_or_asset_id = traderPrivateDeleteV2PositionsSymbolOrAssetId = Entry('v2/positions/{symbol_or_asset_id}', ['trader', 'private'], 'DELETE', {})
    trader_private_delete_v2_watchlists_watchlist_id = traderPrivateDeleteV2WatchlistsWatchlistId = Entry('v2/watchlists/{watchlist_id}', ['trader', 'private'], 'DELETE', {})
    trader_private_delete_v2_watchlists_by_name = traderPrivateDeleteV2WatchlistsByName = Entry('v2/watchlists:by_name', ['trader', 'private'], 'DELETE', {})
    trader_private_delete_v2_watchlists_watchlist_id_symbol = traderPrivateDeleteV2WatchlistsWatchlistIdSymbol = Entry('v2/watchlists/{watchlist_id}/{symbol}', ['trader', 'private'], 'DELETE', {})
    market_public_get_v1beta3_crypto_loc_bars = marketPublicGetV1beta3CryptoLocBars = Entry('v1beta3/crypto/{loc}/bars', ['market', 'public'], 'GET', {})
    market_public_get_v1beta3_crypto_loc_latest_bars = marketPublicGetV1beta3CryptoLocLatestBars = Entry('v1beta3/crypto/{loc}/latest/bars', ['market', 'public'], 'GET', {})
    market_public_get_v1beta3_crypto_loc_latest_orderbooks = marketPublicGetV1beta3CryptoLocLatestOrderbooks = Entry('v1beta3/crypto/{loc}/latest/orderbooks', ['market', 'public'], 'GET', {})
    market_public_get_v1beta3_crypto_loc_latest_quotes = marketPublicGetV1beta3CryptoLocLatestQuotes = Entry('v1beta3/crypto/{loc}/latest/quotes', ['market', 'public'], 'GET', {})
    market_public_get_v1beta3_crypto_loc_latest_trades = marketPublicGetV1beta3CryptoLocLatestTrades = Entry('v1beta3/crypto/{loc}/latest/trades', ['market', 'public'], 'GET', {})
    market_public_get_v1beta3_crypto_loc_quotes = marketPublicGetV1beta3CryptoLocQuotes = Entry('v1beta3/crypto/{loc}/quotes', ['market', 'public'], 'GET', {})
    market_public_get_v1beta3_crypto_loc_snapshots = marketPublicGetV1beta3CryptoLocSnapshots = Entry('v1beta3/crypto/{loc}/snapshots', ['market', 'public'], 'GET', {})
    market_public_get_v1beta3_crypto_loc_trades = marketPublicGetV1beta3CryptoLocTrades = Entry('v1beta3/crypto/{loc}/trades', ['market', 'public'], 'GET', {})
    market_private_get_v1beta1_corporate_actions = marketPrivateGetV1beta1CorporateActions = Entry('v1beta1/corporate-actions', ['market', 'private'], 'GET', {})
    market_private_get_v1beta1_forex_latest_rates = marketPrivateGetV1beta1ForexLatestRates = Entry('v1beta1/forex/latest/rates', ['market', 'private'], 'GET', {})
    market_private_get_v1beta1_forex_rates = marketPrivateGetV1beta1ForexRates = Entry('v1beta1/forex/rates', ['market', 'private'], 'GET', {})
    market_private_get_v1beta1_logos_symbol = marketPrivateGetV1beta1LogosSymbol = Entry('v1beta1/logos/{symbol}', ['market', 'private'], 'GET', {})
    market_private_get_v1beta1_news = marketPrivateGetV1beta1News = Entry('v1beta1/news', ['market', 'private'], 'GET', {})
    market_private_get_v1beta1_screener_stocks_most_actives = marketPrivateGetV1beta1ScreenerStocksMostActives = Entry('v1beta1/screener/stocks/most-actives', ['market', 'private'], 'GET', {})
    market_private_get_v1beta1_screener_market_type_movers = marketPrivateGetV1beta1ScreenerMarketTypeMovers = Entry('v1beta1/screener/{market_type}/movers', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_auctions = marketPrivateGetV2StocksAuctions = Entry('v2/stocks/auctions', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_bars = marketPrivateGetV2StocksBars = Entry('v2/stocks/bars', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_bars_latest = marketPrivateGetV2StocksBarsLatest = Entry('v2/stocks/bars/latest', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_meta_conditions_ticktype = marketPrivateGetV2StocksMetaConditionsTicktype = Entry('v2/stocks/meta/conditions/{ticktype}', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_meta_exchanges = marketPrivateGetV2StocksMetaExchanges = Entry('v2/stocks/meta/exchanges', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_quotes = marketPrivateGetV2StocksQuotes = Entry('v2/stocks/quotes', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_quotes_latest = marketPrivateGetV2StocksQuotesLatest = Entry('v2/stocks/quotes/latest', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_snapshots = marketPrivateGetV2StocksSnapshots = Entry('v2/stocks/snapshots', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_trades = marketPrivateGetV2StocksTrades = Entry('v2/stocks/trades', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_trades_latest = marketPrivateGetV2StocksTradesLatest = Entry('v2/stocks/trades/latest', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_symbol_auctions = marketPrivateGetV2StocksSymbolAuctions = Entry('v2/stocks/{symbol}/auctions', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_symbol_bars = marketPrivateGetV2StocksSymbolBars = Entry('v2/stocks/{symbol}/bars', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_symbol_bars_latest = marketPrivateGetV2StocksSymbolBarsLatest = Entry('v2/stocks/{symbol}/bars/latest', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_symbol_quotes = marketPrivateGetV2StocksSymbolQuotes = Entry('v2/stocks/{symbol}/quotes', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_symbol_quotes_latest = marketPrivateGetV2StocksSymbolQuotesLatest = Entry('v2/stocks/{symbol}/quotes/latest', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_symbol_snapshot = marketPrivateGetV2StocksSymbolSnapshot = Entry('v2/stocks/{symbol}/snapshot', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_symbol_trades = marketPrivateGetV2StocksSymbolTrades = Entry('v2/stocks/{symbol}/trades', ['market', 'private'], 'GET', {})
    market_private_get_v2_stocks_symbol_trades_latest = marketPrivateGetV2StocksSymbolTradesLatest = Entry('v2/stocks/{symbol}/trades/latest', ['market', 'private'], 'GET', {})
