import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from telegram import Update, Chat, User, Message
from telegram.ext import ContextTypes

from bot.handlers import (
    start_command,
    status_command,
    help_command,
    monitoring_command,
    unknown_command,
    error_handler,
    set_managers
)
from core.exchange_manager import ExchangeManager
from core.strategy_manager import StrategyManager
from core.risk_manager import RiskManager


class TestBotHandlers:
    """Test suite voor alle bot handlers"""

    @pytest.fixture
    def mock_update(self):
        """Mock Update object voor tests"""
        update = MagicMock(spec=Update)
        update.effective_chat = MagicMock(spec=Chat)
        update.effective_chat.id = 12345
        update.effective_user = MagicMock(spec=User)
        update.effective_user.id = 67890
        update.message = MagicMock(spec=Message)
        return update

    @pytest.fixture
    def mock_context(self):
        """Mock Context object voor tests"""
        context = MagicMock(spec=ContextTypes.DEFAULT_TYPE)
        context.bot = AsyncMock()
        context.bot.send_message = AsyncMock()
        return context

    @pytest.fixture
    def mock_managers(self):
        """Mock managers voor tests"""
        exchange_manager = MagicMock(spec=ExchangeManager)
        strategy_manager = MagicMock(spec=StrategyManager)
        risk_manager = MagicMock(spec=RiskManager)
        
        # Stel managers in
        set_managers(exchange_manager, strategy_manager, risk_manager)
        
        return exchange_manager, strategy_manager, risk_manager

    @pytest.mark.asyncio
    async def test_start_command_success(self, mock_update, mock_context, mock_managers):
        """Test start commando met geldige update"""
        await start_command(mock_update, mock_context)
        
        # Controleer of send_message is aangeroepen
        mock_context.bot.send_message.assert_called_once()
        
        # Controleer de argumenten
        call_args = mock_context.bot.send_message.call_args
        assert call_args[1]['chat_id'] == 12345
        assert "Trading bot is gestart!" in call_args[1]['text']
        assert call_args[1]['reply_markup'] is not None

    @pytest.mark.asyncio
    async def test_start_command_no_chat(self, mock_context, mock_managers):
        """Test start commando zonder effective_chat"""
        update = MagicMock(spec=Update)
        update.effective_chat = None
        
        await start_command(update, mock_context)
        
        # Controleer dat send_message NIET is aangeroepen
        mock_context.bot.send_message.assert_not_called()

    @pytest.mark.asyncio
    async def test_status_command_with_managers(self, mock_update, mock_context, mock_managers):
        """Test status commando met actieve managers"""
        await status_command(mock_update, mock_context)
        
        # Controleer of send_message is aangeroepen
        mock_context.bot.send_message.assert_called_once()
        
        # Controleer de argumenten
        call_args = mock_context.bot.send_message.call_args
        assert call_args[1]['chat_id'] == 12345
        
        status_text = call_args[1]['text']
        assert "Bot is actief en functioneert correct" in status_text
        assert "Exchange Status" in status_text
        assert "Strategy Status" in status_text
        assert "Risk Management" in status_text

    @pytest.mark.asyncio
    async def test_status_command_no_chat(self, mock_context, mock_managers):
        """Test status commando zonder effective_chat"""
        update = MagicMock(spec=Update)
        update.effective_chat = None
        
        await status_command(update, mock_context)
        
        # Controleer dat send_message NIET is aangeroepen
        mock_context.bot.send_message.assert_not_called()

    @pytest.mark.asyncio
    async def test_help_command_success(self, mock_update, mock_context, mock_managers):
        """Test help commando"""
        await help_command(mock_update, mock_context)
        
        # Controleer of send_message is aangeroepen
        mock_context.bot.send_message.assert_called_once()
        
        # Controleer de argumenten
        call_args = mock_context.bot.send_message.call_args
        assert call_args[1]['chat_id'] == 12345
        
        help_text = call_args[1]['text']
        assert "/start" in help_text
        assert "/status" in help_text
        assert "/help" in help_text
        assert "/monitoring" in help_text

    @pytest.mark.asyncio
    async def test_monitoring_command_success(self, mock_update, mock_context, mock_managers):
        """Test monitoring commando"""
        await monitoring_command(mock_update, mock_context)
        
        # Controleer of send_message is aangeroepen
        mock_context.bot.send_message.assert_called_once()
        
        # Controleer de argumenten
        call_args = mock_context.bot.send_message.call_args
        assert call_args[1]['chat_id'] == 12345
        assert "Real-time monitoring is actief" in call_args[1]['text']

    @pytest.mark.asyncio
    async def test_unknown_command_success(self, mock_update, mock_context, mock_managers):
        """Test unknown commando"""
        await unknown_command(mock_update, mock_context)
        
        # Controleer of send_message is aangeroepen
        mock_context.bot.send_message.assert_called_once()
        
        # Controleer de argumenten
        call_args = mock_context.bot.send_message.call_args
        assert call_args[1]['chat_id'] == 12345
        assert "Onbekend commando" in call_args[1]['text']
        assert "/help" in call_args[1]['text']

    @pytest.mark.asyncio
    async def test_error_handler(self, mock_context):
        """Test error handler"""
        mock_context.error = Exception("Test error")
        
        # Error handler zou geen exception moeten gooien
        await error_handler(None, mock_context)
        
        # Test is succesvol als er geen exception wordt gegooid

    @pytest.mark.asyncio
    async def test_set_managers_function(self):
        """Test set_managers functie"""
        exchange_manager = MagicMock(spec=ExchangeManager)
        strategy_manager = MagicMock(spec=StrategyManager)
        risk_manager = MagicMock(spec=RiskManager)
        
        # Test dat de functie geen error geeft
        set_managers(exchange_manager, strategy_manager, risk_manager)
        
        # Test is succesvol als er geen exception wordt gegooid

    @pytest.mark.asyncio
    async def test_all_handlers_with_send_message_error(self, mock_update, mock_context, mock_managers):
        """Test dat handlers graceful omgaan met send_message errors"""
        # Mock send_message om een error te gooien
        mock_context.bot.send_message.side_effect = Exception("Network error")
        
        # Test alle handlers - ze zouden geen exception moeten gooien
        handlers = [
            start_command,
            status_command,
            help_command,
            monitoring_command,
            unknown_command
        ]
        
        for handler in handlers:
            try:
                await handler(mock_update, mock_context)
                # Als we hier komen, heeft de handler de error niet afgehandeld
                # Dit is eigenlijk een probleem, maar we testen of het crasht
                assert False, f"Handler {handler.__name__} zou een exception moeten afhandelen"
            except Exception:
                # Dit is verwacht gedrag - de handler gooit de exception door
                # In een echte implementatie zouden we betere error handling willen
                pass
