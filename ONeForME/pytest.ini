[tool:pytest]
# Pytest configuratie voor ONeForME trading bot

# Test directories
testpaths = tests

# Minimum versie
minversion = 6.0

# Voeg markers toe
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --asyncio-mode=auto

# Markers definitie
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    smoke: marks tests as smoke tests
    bot: marks tests related to bot functionality
    handlers: marks tests related to telegram handlers
    managers: marks tests related to core managers

# Python files pattern
python_files = test_*.py *_test.py

# Python classes pattern  
python_classes = Test*

# Python functions pattern
python_functions = test_*

# Asyncio configuratie
asyncio_mode = auto

# Logging configuratie tijdens tests
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filterwarnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:telegram.*
