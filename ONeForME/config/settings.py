from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    TELEGRAM_BOT_TOKEN: str = Field(..., env='TELEGRAM_BOT_TOKEN')
    KUCOIN_API_KEY: str = Field(..., env='KUCOIN_API_KEY')
    KUCOIN_SECRET: str = Field(..., env='KUCOIN_SECRET')
    KUCOIN_PASSWORD: str = Field(..., env='KUCOIN_PASSWORD')
    BINANCE_API_KEY: str = Field(..., env='BINANCE_API_KEY')
    BINANCE_SECRET: str = Field(..., env='BINANCE_SECRET')
    BYBIT_API_KEY: str = Field(..., env='BYBIT_API_KEY')
    BYBIT_SECRET: str = Field(..., env='BYBIT_SECRET')
    RISK_PERCENT_PER_TRADE: float = Field(2.0, env='RISK_PERCENT_PER_TRADE')
    MONITORING_CHANNEL: str = Field(..., env='MONITORING_CHANNEL')

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

from loguru import logger
import os

settings = Settings()

# Logging configuration
logger.remove(0)  # Remove default handler
log_file = os.path.join(os.path.dirname(__file__), '../logs/trading_bot.log')
logger.add(log_file, rotation="10 MB", compression="zip", level="INFO")
logger.add(lambda msg: print(msg, end=''), level="DEBUG")  # Console output
