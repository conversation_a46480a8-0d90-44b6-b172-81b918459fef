from ccxt.base.types import Entry


class ImplicitAPI:
    addressbook_get_me = addressbookGetMe = Entry('me', 'addressbook', 'GET', {})
    addressbook_post = addressbookPost = Entry('', 'addressbook', 'POST', {})
    addressbook_post_id_id = addressbookPostIdId = Entry('id/{id}', 'addressbook', 'POST', {})
    addressbook_post_id_id_remove = addressbookPostIdIdRemove = Entry('id/{id}/remove', 'addressbook', 'POST', {})
    custody_get_credentials = custodyGetCredentials = Entry('credentials', 'custody', 'GET', {})
    custody_get_credentials_h_hash = custodyGetCredentialsHHash = Entry('credentials/h/{hash}', 'custody', 'GET', {})
    custody_get_credentials_k_key = custodyGetCredentialsKKey = Entry('credentials/k/{key}', 'custody', 'GET', {})
    custody_get_credentials_me = custodyGetCredentialsMe = Entry('credentials/me', 'custody', 'GET', {})
    custody_get_credentials_me_address = custodyGetCredentialsMeAddress = Entry('credentials/me/address', 'custody', 'GET', {})
    custody_get_deposit_addresses = custodyGetDepositAddresses = Entry('deposit-addresses', 'custody', 'GET', {})
    custody_get_deposit_addresses_h_hash = custodyGetDepositAddressesHHash = Entry('deposit-addresses/h/{hash}', 'custody', 'GET', {})
    history_get_orders = historyGetOrders = Entry('orders', 'history', 'GET', {})
    history_get_orders_details = historyGetOrdersDetails = Entry('orders/details', 'history', 'GET', {})
    history_get_orders_export_csv = historyGetOrdersExportCsv = Entry('orders/export/csv', 'history', 'GET', {})
    history_get_trades = historyGetTrades = Entry('trades', 'history', 'GET', {})
    history_get_trades_export_csv = historyGetTradesExportCsv = Entry('trades/export/csv', 'history', 'GET', {})
    currencies_get_a_address = currenciesGetAAddress = Entry('a/{address}', 'currencies', 'GET', {})
    currencies_get_i_id = currenciesGetIId = Entry('i/{id}', 'currencies', 'GET', {})
    currencies_get_s_symbol = currenciesGetSSymbol = Entry('s/{symbol}', 'currencies', 'GET', {})
    currencies_post_perform = currenciesPostPerform = Entry('perform', 'currencies', 'POST', {})
    currencies_post_prepare = currenciesPostPrepare = Entry('prepare', 'currencies', 'POST', {})
    currencies_post_remove_perform = currenciesPostRemovePerform = Entry('remove/perform', 'currencies', 'POST', {})
    currencies_post_s_symbol_remove_prepare = currenciesPostSSymbolRemovePrepare = Entry('s/{symbol}/remove/prepare', 'currencies', 'POST', {})
    currencies_post_s_symbol_update_perform = currenciesPostSSymbolUpdatePerform = Entry('s/{symbol}/update/perform', 'currencies', 'POST', {})
    currencies_post_s_symbol_update_prepare = currenciesPostSSymbolUpdatePrepare = Entry('s/{symbol}/update/prepare', 'currencies', 'POST', {})
    manager_get_deposits = managerGetDeposits = Entry('deposits', 'manager', 'GET', {})
    manager_get_transfers = managerGetTransfers = Entry('transfers', 'manager', 'GET', {})
    manager_get_withdrawals = managerGetWithdrawals = Entry('withdrawals', 'manager', 'GET', {})
    markets_get_i_id = marketsGetIId = Entry('i/{id}', 'markets', 'GET', {})
    markets_get_s_symbol = marketsGetSSymbol = Entry('s/{symbol}', 'markets', 'GET', {})
    markets_post_perform = marketsPostPerform = Entry('perform', 'markets', 'POST', {})
    markets_post_prepare = marketsPostPrepare = Entry('prepare', 'markets', 'POST', {})
    markets_post_remove_perform = marketsPostRemovePerform = Entry('remove/perform', 'markets', 'POST', {})
    markets_post_s_symbol_remove_prepare = marketsPostSSymbolRemovePrepare = Entry('s/{symbol}/remove/prepare', 'markets', 'POST', {})
    markets_post_s_symbol_update_perform = marketsPostSSymbolUpdatePerform = Entry('s/{symbol}/update/perform', 'markets', 'POST', {})
    markets_post_s_symbol_update_prepare = marketsPostSSymbolUpdatePrepare = Entry('s/{symbol}/update/prepare', 'markets', 'POST', {})
    public_get_candles = publicGetCandles = Entry('candles', 'public', 'GET', {})
    public_get_currencies = publicGetCurrencies = Entry('currencies', 'public', 'GET', {})
    public_get_markets = publicGetMarkets = Entry('markets', 'public', 'GET', {})
    public_get_orderbook = publicGetOrderbook = Entry('orderbook', 'public', 'GET', {})
    public_get_orderbook_raw = publicGetOrderbookRaw = Entry('orderbook/raw', 'public', 'GET', {})
    public_get_orderbook_v2 = publicGetOrderbookV2 = Entry('orderbook/v2', 'public', 'GET', {})
    public_get_tickers = publicGetTickers = Entry('tickers', 'public', 'GET', {})
    public_get_trades = publicGetTrades = Entry('trades', 'public', 'GET', {})
    statistics_get_address = statisticsGetAddress = Entry('address', 'statistics', 'GET', {})
    trading_get_balances = tradingGetBalances = Entry('balances', 'trading', 'GET', {})
    trading_get_fees = tradingGetFees = Entry('fees', 'trading', 'GET', {})
    trading_get_orders = tradingGetOrders = Entry('orders', 'trading', 'GET', {})
    trading_post_orders = tradingPostOrders = Entry('orders', 'trading', 'POST', {})
    trading_post_orders_json = tradingPostOrdersJson = Entry('orders/json', 'trading', 'POST', {})
    trading_put_orders = tradingPutOrders = Entry('orders', 'trading', 'PUT', {})
    trading_put_orders_json = tradingPutOrdersJson = Entry('orders/json', 'trading', 'PUT', {})
    trading_delete_orders = tradingDeleteOrders = Entry('orders', 'trading', 'DELETE', {})
    trading_delete_orders_json = tradingDeleteOrdersJson = Entry('orders/json', 'trading', 'DELETE', {})
    tradingview_get_config = tradingviewGetConfig = Entry('config', 'tradingview', 'GET', {})
    tradingview_get_history = tradingviewGetHistory = Entry('history', 'tradingview', 'GET', {})
    tradingview_get_symbol_info = tradingviewGetSymbolInfo = Entry('symbol_info', 'tradingview', 'GET', {})
    tradingview_get_time = tradingviewGetTime = Entry('time', 'tradingview', 'GET', {})
