from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_orderbook = publicGetOrderbook = Entry('orderbook', 'public', 'GET', {})
    public_get_ticker = publicGetTicker = Entry('ticker', 'public', 'GET', {})
    public_get_ticker_utc = publicGetTickerUtc = Entry('ticker_utc', 'public', 'GET', {})
    public_get_trades = publicGetTrades = Entry('trades', 'public', 'GET', {})
    v2public_get_range_units = v2PublicGetRangeUnits = Entry('range_units', 'v2Public', 'GET', {})
    v2public_get_markets_quote_currency = v2PublicGetMarketsQuoteCurrency = Entry('markets/{quote_currency}', 'v2Public', 'GET', {})
    v2public_get_markets_quote_currency_target_currency = v2PublicGetMarketsQuoteCurrencyTargetCurrency = Entry('markets/{quote_currency}/{target_currency}', 'v2Public', 'GET', {})
    v2public_get_orderbook_quote_currency_target_currency = v2PublicGetOrderbookQuoteCurrencyTargetCurrency = Entry('orderbook/{quote_currency}/{target_currency}', 'v2Public', 'GET', {})
    v2public_get_trades_quote_currency_target_currency = v2PublicGetTradesQuoteCurrencyTargetCurrency = Entry('trades/{quote_currency}/{target_currency}', 'v2Public', 'GET', {})
    v2public_get_ticker_new_quote_currency = v2PublicGetTickerNewQuoteCurrency = Entry('ticker_new/{quote_currency}', 'v2Public', 'GET', {})
    v2public_get_ticker_new_quote_currency_target_currency = v2PublicGetTickerNewQuoteCurrencyTargetCurrency = Entry('ticker_new/{quote_currency}/{target_currency}', 'v2Public', 'GET', {})
    v2public_get_ticker_utc_new_quote_currency = v2PublicGetTickerUtcNewQuoteCurrency = Entry('ticker_utc_new/{quote_currency}', 'v2Public', 'GET', {})
    v2public_get_ticker_utc_new_quote_currency_target_currency = v2PublicGetTickerUtcNewQuoteCurrencyTargetCurrency = Entry('ticker_utc_new/{quote_currency}/{target_currency}', 'v2Public', 'GET', {})
    v2public_get_currencies = v2PublicGetCurrencies = Entry('currencies', 'v2Public', 'GET', {})
    v2public_get_currencies_currency = v2PublicGetCurrenciesCurrency = Entry('currencies/{currency}', 'v2Public', 'GET', {})
    v2public_get_chart_quote_currency_target_currency = v2PublicGetChartQuoteCurrencyTargetCurrency = Entry('chart/{quote_currency}/{target_currency}', 'v2Public', 'GET', {})
    private_post_account_deposit_address = privatePostAccountDepositAddress = Entry('account/deposit_address', 'private', 'POST', {})
    private_post_account_btc_deposit_address = privatePostAccountBtcDepositAddress = Entry('account/btc_deposit_address', 'private', 'POST', {})
    private_post_account_balance = privatePostAccountBalance = Entry('account/balance', 'private', 'POST', {})
    private_post_account_daily_balance = privatePostAccountDailyBalance = Entry('account/daily_balance', 'private', 'POST', {})
    private_post_account_user_info = privatePostAccountUserInfo = Entry('account/user_info', 'private', 'POST', {})
    private_post_account_virtual_account = privatePostAccountVirtualAccount = Entry('account/virtual_account', 'private', 'POST', {})
    private_post_order_cancel_all = privatePostOrderCancelAll = Entry('order/cancel_all', 'private', 'POST', {})
    private_post_order_cancel = privatePostOrderCancel = Entry('order/cancel', 'private', 'POST', {})
    private_post_order_limit_buy = privatePostOrderLimitBuy = Entry('order/limit_buy', 'private', 'POST', {})
    private_post_order_limit_sell = privatePostOrderLimitSell = Entry('order/limit_sell', 'private', 'POST', {})
    private_post_order_complete_orders = privatePostOrderCompleteOrders = Entry('order/complete_orders', 'private', 'POST', {})
    private_post_order_limit_orders = privatePostOrderLimitOrders = Entry('order/limit_orders', 'private', 'POST', {})
    private_post_order_order_info = privatePostOrderOrderInfo = Entry('order/order_info', 'private', 'POST', {})
    private_post_transaction_auth_number = privatePostTransactionAuthNumber = Entry('transaction/auth_number', 'private', 'POST', {})
    private_post_transaction_history = privatePostTransactionHistory = Entry('transaction/history', 'private', 'POST', {})
    private_post_transaction_krw_history = privatePostTransactionKrwHistory = Entry('transaction/krw/history', 'private', 'POST', {})
    private_post_transaction_btc = privatePostTransactionBtc = Entry('transaction/btc', 'private', 'POST', {})
    private_post_transaction_coin = privatePostTransactionCoin = Entry('transaction/coin', 'private', 'POST', {})
    v2private_post_account_balance = v2PrivatePostAccountBalance = Entry('account/balance', 'v2Private', 'POST', {})
    v2private_post_account_deposit_address = v2PrivatePostAccountDepositAddress = Entry('account/deposit_address', 'v2Private', 'POST', {})
    v2private_post_account_user_info = v2PrivatePostAccountUserInfo = Entry('account/user_info', 'v2Private', 'POST', {})
    v2private_post_account_virtual_account = v2PrivatePostAccountVirtualAccount = Entry('account/virtual_account', 'v2Private', 'POST', {})
    v2private_post_order_cancel = v2PrivatePostOrderCancel = Entry('order/cancel', 'v2Private', 'POST', {})
    v2private_post_order_limit_buy = v2PrivatePostOrderLimitBuy = Entry('order/limit_buy', 'v2Private', 'POST', {})
    v2private_post_order_limit_sell = v2PrivatePostOrderLimitSell = Entry('order/limit_sell', 'v2Private', 'POST', {})
    v2private_post_order_limit_orders = v2PrivatePostOrderLimitOrders = Entry('order/limit_orders', 'v2Private', 'POST', {})
    v2private_post_order_complete_orders = v2PrivatePostOrderCompleteOrders = Entry('order/complete_orders', 'v2Private', 'POST', {})
    v2private_post_order_query_order = v2PrivatePostOrderQueryOrder = Entry('order/query_order', 'v2Private', 'POST', {})
    v2private_post_transaction_auth_number = v2PrivatePostTransactionAuthNumber = Entry('transaction/auth_number', 'v2Private', 'POST', {})
    v2private_post_transaction_btc = v2PrivatePostTransactionBtc = Entry('transaction/btc', 'v2Private', 'POST', {})
    v2private_post_transaction_history = v2PrivatePostTransactionHistory = Entry('transaction/history', 'v2Private', 'POST', {})
    v2private_post_transaction_krw_history = v2PrivatePostTransactionKrwHistory = Entry('transaction/krw/history', 'v2Private', 'POST', {})
    v2_1private_post_account_balance_all = v2_1PrivatePostAccountBalanceAll = Entry('account/balance/all', 'v2_1Private', 'POST', {})
    v2_1private_post_account_balance = v2_1PrivatePostAccountBalance = Entry('account/balance', 'v2_1Private', 'POST', {})
    v2_1private_post_account_trade_fee = v2_1PrivatePostAccountTradeFee = Entry('account/trade_fee', 'v2_1Private', 'POST', {})
    v2_1private_post_account_trade_fee_quote_currency_target_currency = v2_1PrivatePostAccountTradeFeeQuoteCurrencyTargetCurrency = Entry('account/trade_fee/{quote_currency}/{target_currency}', 'v2_1Private', 'POST', {})
    v2_1private_post_order_limit = v2_1PrivatePostOrderLimit = Entry('order/limit', 'v2_1Private', 'POST', {})
    v2_1private_post_order_cancel = v2_1PrivatePostOrderCancel = Entry('order/cancel', 'v2_1Private', 'POST', {})
    v2_1private_post_order_cancel_all = v2_1PrivatePostOrderCancelAll = Entry('order/cancel/all', 'v2_1Private', 'POST', {})
    v2_1private_post_order_open_orders = v2_1PrivatePostOrderOpenOrders = Entry('order/open_orders', 'v2_1Private', 'POST', {})
    v2_1private_post_order_open_orders_all = v2_1PrivatePostOrderOpenOrdersAll = Entry('order/open_orders/all', 'v2_1Private', 'POST', {})
    v2_1private_post_order_complete_orders = v2_1PrivatePostOrderCompleteOrders = Entry('order/complete_orders', 'v2_1Private', 'POST', {})
    v2_1private_post_order_complete_orders_all = v2_1PrivatePostOrderCompleteOrdersAll = Entry('order/complete_orders/all', 'v2_1Private', 'POST', {})
    v2_1private_post_order_info = v2_1PrivatePostOrderInfo = Entry('order/info', 'v2_1Private', 'POST', {})
    v2_1private_post_transaction_krw_history = v2_1PrivatePostTransactionKrwHistory = Entry('transaction/krw/history', 'v2_1Private', 'POST', {})
    v2_1private_post_transaction_coin_history = v2_1PrivatePostTransactionCoinHistory = Entry('transaction/coin/history', 'v2_1Private', 'POST', {})
    v2_1private_post_transaction_coin_withdrawal_limit = v2_1PrivatePostTransactionCoinWithdrawalLimit = Entry('transaction/coin/withdrawal/limit', 'v2_1Private', 'POST', {})
