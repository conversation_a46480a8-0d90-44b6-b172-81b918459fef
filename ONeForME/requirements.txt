# Core dependencies
python-telegram-bot==20.8
ccxt==4.2.25
pydantic==2.8.2
pydantic-settings==2.2.1
python-dotenv==1.0.1

# Async & utilities
aiohttp==3.9.3
asyncio==3.4.3
tenacity>=8.0.0

# Logging & monitoring
loguru>=0.6.0
rich==13.7.0

# Data & calculations
pandas>=1.5.0
numpy==1.26.4
ta==0.11.0

# Database
aiosqlite==0.19.0
sqlalchemy==2.0.27

# Testing
pytest==8.0.0
pytest-asyncio==0.23.5
pytest-mock==3.12.0

# Development
black==24.2.0
isort==5.13.2
mypy==1.8.0
