#!/usr/bin/env python3
"""
🚨 VEILIGHEIDSCHECK VOOR LIVE TRADING 🚨

Dit script controleert of je bot veilig is ingesteld voor live trading met echt geld.
Voer dit ALTIJD uit voordat je live trading start!

Gebruik: python safety_check.py
"""

import sys
import os
from config.settings import settings
from loguru import logger

def print_header():
    """Print header met waarschuwing"""
    print("=" * 60)
    print("🚨 TRADING BOT VEILIGHEIDSCHECK 🚨")
    print("=" * 60)
    print()

def print_section(title: str):
    """Print sectie header"""
    print(f"\n📋 {title}")
    print("-" * 40)

def check_basic_config():
    """Controleer basis configuratie"""
    print_section("BASIS CONFIGURATIE")
    
    issues = []
    
    # Telegram bot token
    if not settings.TELEGRAM_BOT_TOKEN:
        issues.append("❌ Telegram bot token niet ingesteld")
    else:
        print("✅ Telegram bot token ingesteld")
    
    # Admin users
    if not settings.admin_user_ids_list:
        issues.append("❌ Geen admin users ingesteld")
    else:
        print(f"✅ Admin users: {len(settings.admin_user_ids_list)} gebruikers")
    
    # Exchange API keys
    exchanges = []
    if settings.BINANCE_API_KEY and settings.BINANCE_SECRET:
        exchanges.append("Binance")
    if settings.KUCOIN_API_KEY and settings.KUCOIN_SECRET and settings.KUCOIN_PASSWORD:
        exchanges.append("KuCoin")
    
    if not exchanges:
        issues.append("❌ Geen exchange API keys ingesteld")
    else:
        print(f"✅ Exchange API keys: {', '.join(exchanges)}")
    
    return issues

def check_risk_settings():
    """Controleer risk management instellingen"""
    print_section("RISK MANAGEMENT")
    
    issues = []
    warnings = []
    
    # Risk per trade
    if settings.RISK_PERCENT_PER_TRADE > 2.0:
        issues.append(f"❌ Risk per trade te hoog: {settings.RISK_PERCENT_PER_TRADE}% (aanbevolen: max 2%)")
    elif settings.RISK_PERCENT_PER_TRADE > 1.0:
        warnings.append(f"⚠️ Risk per trade hoog: {settings.RISK_PERCENT_PER_TRADE}% (aanbevolen: max 1%)")
    else:
        print(f"✅ Risk per trade: {settings.RISK_PERCENT_PER_TRADE}%")
    
    # Max concurrent positions
    if settings.MAX_CONCURRENT_POSITIONS > 3:
        issues.append(f"❌ Te veel concurrent positions: {settings.MAX_CONCURRENT_POSITIONS} (aanbevolen: max 3)")
    else:
        print(f"✅ Max concurrent positions: {settings.MAX_CONCURRENT_POSITIONS}")
    
    # Max drawdown
    if settings.MAX_DRAWDOWN_PERCENT > 10.0:
        warnings.append(f"⚠️ Max drawdown hoog: {settings.MAX_DRAWDOWN_PERCENT}% (aanbevolen: max 10%)")
    else:
        print(f"✅ Max drawdown: {settings.MAX_DRAWDOWN_PERCENT}%")
    
    # Stop loss en take profit
    print(f"✅ Default stop loss: {settings.DEFAULT_STOP_LOSS_PERCENT}%")
    print(f"✅ Default take profit: {settings.DEFAULT_TAKE_PROFIT_PERCENT}%")
    
    # Daily trade limit
    print(f"✅ Max daily trades: {settings.MAX_DAILY_TRADES}")
    
    return issues, warnings

def check_trading_mode():
    """Controleer trading mode instellingen"""
    print_section("TRADING MODE")
    
    issues = []
    
    print(f"Paper trading: {'Aan' if settings.PAPER_TRADING else 'Uit'}")
    print(f"Live trading enabled: {'Aan' if settings.LIVE_TRADING_ENABLED else 'Uit'}")
    print(f"Emergency stop: {'Aan' if settings.EMERGENCY_STOP else 'Uit'}")
    
    if settings.EMERGENCY_STOP:
        issues.append("❌ Emergency stop is geactiveerd")
    
    if not settings.LIVE_TRADING_ENABLED:
        issues.append("❌ Live trading is niet expliciet enabled")
    
    if settings.PAPER_TRADING and settings.LIVE_TRADING_ENABLED:
        issues.append("❌ Paper trading en live trading kunnen niet beide aan staan")
    
    return issues

def check_environment():
    """Controleer environment instellingen"""
    print_section("ENVIRONMENT")
    
    issues = []
    
    print(f"Environment: {settings.ENVIRONMENT}")
    print(f"Debug mode: {'Aan' if settings.DEBUG else 'Uit'}")
    
    if settings.ENVIRONMENT == "development" and settings.LIVE_TRADING_ENABLED:
        issues.append("❌ Live trading in development environment niet aanbevolen")
    
    # Check .env file
    env_file = os.path.join(os.path.dirname(__file__), '.env')
    if os.path.exists(env_file):
        print("✅ .env file gevonden")
    else:
        issues.append("❌ .env file niet gevonden")
    
    return issues

def main():
    """Hoofdfunctie voor veiligheidscheck"""
    print_header()
    
    all_issues = []
    all_warnings = []
    
    # Voer alle checks uit
    all_issues.extend(check_basic_config())
    
    risk_issues, risk_warnings = check_risk_settings()
    all_issues.extend(risk_issues)
    all_warnings.extend(risk_warnings)
    
    all_issues.extend(check_trading_mode())
    all_issues.extend(check_environment())
    
    # Toon resultaten
    print_section("RESULTATEN")
    
    if all_warnings:
        print("⚠️ WAARSCHUWINGEN:")
        for warning in all_warnings:
            print(f"  {warning}")
        print()
    
    if all_issues:
        print("❌ KRITIEKE PROBLEMEN:")
        for issue in all_issues:
            print(f"  {issue}")
        print()
        print("🚨 LIVE TRADING NIET VEILIG!")
        print("Los alle problemen op voordat je live trading start.")
        return False
    else:
        print("✅ ALLE CHECKS GESLAAGD!")
        print()
        if all_warnings:
            print("⚠️ Er zijn waarschuwingen, maar live trading is technisch mogelijk.")
        else:
            print("🎉 Live trading kan veilig worden gestart!")
        return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ Fout tijdens veiligheidscheck: {e}")
        sys.exit(1)
