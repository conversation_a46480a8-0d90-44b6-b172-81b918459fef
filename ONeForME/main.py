import asyncio
from telegram.ext import ApplicationBuilder
from bot.handlers import (
    start_handler,
    status_handler,
    help_handler,
    monitoring_handler,
    safety_check_handler,
    emergency_stop_handler,
    unknown_handler,
    error_handler,
    set_managers,
)
from core.exchange_manager import ExchangeManager
from core.strategy_manager import StrategyManager
from core.risk_manager import RiskManager
from config.settings import settings
from loguru import logger


async def main():
    # Initialiseer managers
    exchange_manager = ExchangeManager()
    strategy_manager = StrategyManager(exchange_manager)
    risk_manager = RiskManager(exchange_manager)

    # Stel managers in voor handlers
    set_managers(exchange_manager, strategy_manager, risk_manager)

    # Initialiseer Telegram-bot
    application = ApplicationBuilder().token(settings.TELEGRAM_BOT_TOKEN).build()

    # Registreer commando's
    application.add_handler(start_handler)
    application.add_handler(status_handler)
    application.add_handler(help_handler)
    application.add_handler(monitoring_handler)
    application.add_handler(safety_check_handler)
    application.add_handler(emergency_stop_handler)
    application.add_handler(unknown_handler)
    application.add_error_handler(error_handler)

    # Start de bot
    logger.info("Telegram-bot wordt gestart...")

    try:
        # Start de bot met polling - dit blokkeert tot de bot wordt gestopt
        application.run_polling()
    except KeyboardInterrupt:
        logger.info("Bot wordt gestopt door gebruiker...")
    except Exception as e:
        logger.error(f"Fout bij starten van bot: {e}")
    finally:
        # Netjes afsluiten
        try:
            await exchange_manager.close_exchanges()
        except Exception as e:
            logger.error(f"Fout bij afsluiten van exchanges: {e}")
        logger.info("Telegram-bot en exchanges zijn gestopt")


if __name__ == "__main__":
    asyncio.run(main())
