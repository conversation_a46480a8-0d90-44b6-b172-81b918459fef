from typing import Dict, List, Optional
from strategies.base_strategy import BaseStrategy
from core.exchange_manager import ExchangeManager
from config.settings import settings
from loguru import logger

class StrategyManager:
    def __init__(self, exchange_manager: ExchangeManager):
        self.exchange_manager = exchange_manager
        self.strategies: Dict[str, BaseStrategy] = {}
        
    def add_strategy(self, strategy: BaseStrategy, exchange: str, symbol: str):
        """Voeg een strategie toe voor een specifiek instrument op een exchange"""
        key = f"{exchange}-{symbol}"
        if key in self.strategies:
            logger.warning(f"Strategie voor {key} is reeds geregistreerd")
            return
            
        self.strategies[key] = strategy
        logger.info(f"Strategie {strategy.__class__.__name__} toegevoegd voor {key}")

    async def handle_tick(self, exchange: str, symbol: str):
        """Verwerkt een tick en route naar de relevante strategie"""
        key = f"{exchange}-{symbol}"
        if key not in self.strategies:
            logger.warning(f"Geen strategie gevonden voor {key}")
            return
            
        strategy = self.strategies[key]
        try:
            await strategy.on_tick(exchange, symbol)
        except Exception as e:
            logger.error(f"Fout in strategie {strategy.__class__.__name__}: {e}")

    async def initialize_strategies(self):
        """Initialiseert alle geregistreerde strategieën"""
        for strategy in self.strategies.values():
            try:
                await strategy.initialize()
                logger.info(f"Strategie {strategy.__class__.__name__} geïnitialiseerd")
            except Exception as e:
                logger.error(f"Initialisatie van strategie {strategy.__class__.__name__} mislukt: {e}")
