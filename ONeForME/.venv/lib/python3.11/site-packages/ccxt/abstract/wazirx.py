from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_exchangeinfo = publicGetExchangeInfo = Entry('exchangeInfo', 'public', 'GET', {'cost': 1})
    public_get_depth = publicGetDepth = Entry('depth', 'public', 'GET', {'cost': 1})
    public_get_ping = publicGetPing = Entry('ping', 'public', 'GET', {'cost': 1})
    public_get_systemstatus = publicGetSystemStatus = Entry('systemStatus', 'public', 'GET', {'cost': 1})
    public_get_tickers_24hr = publicGetTickers24hr = Entry('tickers/24hr', 'public', 'GET', {'cost': 1})
    public_get_ticker_24hr = publicGetTicker24hr = Entry('ticker/24hr', 'public', 'GET', {'cost': 1})
    public_get_time = publicGetTime = Entry('time', 'public', 'GET', {'cost': 1})
    public_get_trades = publicGetTrades = Entry('trades', 'public', 'GET', {'cost': 1})
    public_get_klines = publicGetKlines = Entry('klines', 'public', 'GET', {'cost': 1})
    private_get_account = privateGetAccount = Entry('account', 'private', 'GET', {'cost': 1})
    private_get_allorders = privateGetAllOrders = Entry('allOrders', 'private', 'GET', {'cost': 1})
    private_get_funds = privateGetFunds = Entry('funds', 'private', 'GET', {'cost': 1})
    private_get_historicaltrades = privateGetHistoricalTrades = Entry('historicalTrades', 'private', 'GET', {'cost': 1})
    private_get_openorders = privateGetOpenOrders = Entry('openOrders', 'private', 'GET', {'cost': 1})
    private_get_order = privateGetOrder = Entry('order', 'private', 'GET', {'cost': 0.5})
    private_get_mytrades = privateGetMyTrades = Entry('myTrades', 'private', 'GET', {'cost': 0.5})
    private_post_order = privatePostOrder = Entry('order', 'private', 'POST', {'cost': 0.1})
    private_post_order_test = privatePostOrderTest = Entry('order/test', 'private', 'POST', {'cost': 0.5})
    private_post_create_auth_token = privatePostCreateAuthToken = Entry('create_auth_token', 'private', 'POST', {'cost': 1})
    private_delete_order = privateDeleteOrder = Entry('order', 'private', 'DELETE', {'cost': 0.1})
    private_delete_openorders = privateDeleteOpenOrders = Entry('openOrders', 'private', 'DELETE', {'cost': 1})
