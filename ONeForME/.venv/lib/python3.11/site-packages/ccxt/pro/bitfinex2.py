# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

import ccxt.async_support
from ccxt.async_support.base.ws.cache import ArrayCache, ArrayCacheBySymbolById, ArrayCacheByTimestamp
import hashlib
from ccxt.base.types import Balances, Int, Order, OrderBook, Str, Ticker, Trade
from ccxt.async_support.base.ws.client import Client
from typing import List
from ccxt.base.errors import ExchangeError
from ccxt.base.errors import InvalidNonce
from ccxt.base.errors import AuthenticationError
from ccxt.base.precise import Precise


class bitfinex2(ccxt.async_support.bitfinex2):

    def describe(self):
        return self.deep_extend(super(bitfinex2, self).describe(), {
            'has': {
                'ws': True,
                'watchTicker': True,
                'watchTickers': False,
                'watchOrderBook': True,
                'watchTrades': True,
                'watchMyTrades': True,
                'watchBalance': True,
                'watchOHLCV': True,
                'watchOrders': True,
            },
            'urls': {
                'api': {
                    'ws': {
                        'public': 'wss://api-pub.bitfinex.com/ws/2',
                        'private': 'wss://api.bitfinex.com/ws/2',
                    },
                },
            },
            'options': {
                'watchOrderBook': {
                    'prec': 'P0',
                    'freq': 'F0',
                },
                'ordersLimit': 1000,
                'checksum': True,
            },
        })

    async def subscribe(self, channel, symbol, params={}):
        await self.load_markets()
        market = self.market(symbol)
        marketId = market['id']
        url = self.urls['api']['ws']['public']
        client = self.client(url)
        messageHash = channel + ':' + marketId
        request = {
            'event': 'subscribe',
            'channel': channel,
            'symbol': marketId,
        }
        result = await self.watch(url, messageHash, self.deep_extend(request, params), messageHash, {'checksum': False})
        checksum = self.safe_value(self.options, 'checksum', True)
        if checksum and not client.subscriptions[messageHash]['checksum'] and (channel == 'book'):
            client.subscriptions[messageHash]['checksum'] = True
            await client.send({
                'event': 'conf',
                'flags': 131072,
            })
        return result

    async def subscribe_private(self, messageHash):
        await self.load_markets()
        await self.authenticate()
        url = self.urls['api']['ws']['private']
        return await self.watch(url, messageHash, None, 1)

    async def watch_ohlcv(self, symbol: str, timeframe='1m', since: Int = None, limit: Int = None, params={}) -> List[list]:
        """
        watches historical candlestick data containing the open, high, low, and close price, and the volume of a market
        :param str symbol: unified symbol of the market to fetch OHLCV data for
        :param str timeframe: the length of time each candle represents
        :param int [since]: timestamp in ms of the earliest candle to fetch
        :param int [limit]: the maximum amount of candles to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns int[][]: A list of candles ordered, open, high, low, close, volume
        """
        await self.load_markets()
        market = self.market(symbol)
        symbol = market['symbol']
        interval = self.safe_string(self.timeframes, timeframe, timeframe)
        channel = 'candles'
        key = 'trade:' + interval + ':' + market['id']
        messageHash = channel + ':' + interval + ':' + market['id']
        request = {
            'event': 'subscribe',
            'channel': channel,
            'key': key,
        }
        url = self.urls['api']['ws']['public']
        # not using subscribe here because self message has a different format
        ohlcv = await self.watch(url, messageHash, self.deep_extend(request, params), messageHash)
        if self.newUpdates:
            limit = ohlcv.getLimit(symbol, limit)
        return self.filter_by_since_limit(ohlcv, since, limit, 0, True)

    def handle_ohlcv(self, client: Client, message, subscription):
        #
        # initial snapshot
        #   [
        #       341527,  # channel id
        #       [
        #          [
        #             1654705860000,  # timestamp
        #             1802.6,  # open
        #             1800.3,  # close
        #             1802.8,  # high
        #             1800.3,  # low
        #             86.49588236  # volume
        #          ],
        #          [
        #             1654705800000,
        #             1803.6,
        #             1802.6,
        #             1804.9,
        #             1802.3,
        #             74.6348086
        #          ],
        #          [
        #             1654705740000,
        #             1802.5,
        #             1803.2,
        #             1804.4,
        #             1802.4,
        #             23.61801085
        #          ]
        #       ]
        #   ]
        #
        # update
        #   [
        #       211171,
        #       [
        #          1654705680000,
        #          1801,
        #          1802.4,
        #          1802.9,
        #          1800.4,
        #          23.91911091
        #       ]
        #   ]
        #
        data = self.safe_value(message, 1, [])
        ohlcvs = None
        first = self.safe_value(data, 0)
        if isinstance(first, list):
            # snapshot
            ohlcvs = data
        else:
            # update
            ohlcvs = [data]
        channel = self.safe_value(subscription, 'channel')
        key = self.safe_string(subscription, 'key')
        keyParts = key.split(':')
        interval = self.safe_string(keyParts, 1)
        marketId = key
        marketId = marketId.replace('trade:', '')
        marketId = marketId.replace(interval + ':', '')
        market = self.safe_market(marketId)
        timeframe = self.find_timeframe(interval)
        symbol = market['symbol']
        messageHash = channel + ':' + interval + ':' + marketId
        self.ohlcvs[symbol] = self.safe_value(self.ohlcvs, symbol, {})
        stored = self.safe_value(self.ohlcvs[symbol], timeframe)
        if stored is None:
            limit = self.safe_integer(self.options, 'OHLCVLimit', 1000)
            stored = ArrayCacheByTimestamp(limit)
            self.ohlcvs[symbol][timeframe] = stored
        ohlcvsLength = len(ohlcvs)
        for i in range(0, ohlcvsLength):
            ohlcv = ohlcvs[ohlcvsLength - i - 1]
            parsed = self.parse_ohlcv(ohlcv, market)
            stored.append(parsed)
        client.resolve(stored, messageHash)

    async def watch_trades(self, symbol: str, since: Int = None, limit: Int = None, params={}) -> List[Trade]:
        """
        get the list of most recent trades for a particular symbol
        :param str symbol: unified symbol of the market to fetch trades for
        :param int [since]: timestamp in ms of the earliest trade to fetch
        :param int [limit]: the maximum amount of trades to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `trade structures <https://docs.ccxt.com/#/?id=public-trades>`
        """
        trades = await self.subscribe('trades', symbol, params)
        if self.newUpdates:
            limit = trades.getLimit(symbol, limit)
        return self.filter_by_since_limit(trades, since, limit, 'timestamp', True)

    async def watch_my_trades(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Trade]:
        """
        watches information on multiple trades made by the user
        :param str symbol: unified market symbol of the market trades were made in
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trade structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure
        """
        await self.load_markets()
        messageHash = 'myTrade'
        if symbol is not None:
            market = self.market(symbol)
            messageHash += ':' + market['id']
        trades = await self.subscribe_private(messageHash)
        if self.newUpdates:
            limit = trades.getLimit(symbol, limit)
        return self.filter_by_symbol_since_limit(trades, symbol, since, limit, True)

    async def watch_ticker(self, symbol: str, params={}) -> Ticker:
        """
        watches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
        :param str symbol: unified symbol of the market to fetch the ticker for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `ticker structure <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        return await self.subscribe('ticker', symbol, params)

    def handle_my_trade(self, client: Client, message, subscription={}):
        #
        # trade execution
        # [
        #     0,
        #     "te",  # or tu
        #     [
        #        1133411090,
        #        "tLTCUST",
        #        1655110144598,
        #        97084883506,
        #        0.1,
        #        42.821,
        #        "EXCHANGE MARKET",
        #        42.799,
        #        -1,
        #        null,
        #        null,
        #        1655110144596
        #     ]
        # ]
        #
        name = 'myTrade'
        data = self.safe_value(message, 2)
        trade = self.parse_ws_trade(data)
        symbol = trade['symbol']
        market = self.market(symbol)
        messageHash = name + ':' + market['id']
        if self.myTrades is None:
            limit = self.safe_integer(self.options, 'tradesLimit', 1000)
            self.myTrades = ArrayCacheBySymbolById(limit)
        tradesArray = self.myTrades
        tradesArray.append(trade)
        self.myTrades = tradesArray
        # generic subscription
        client.resolve(tradesArray, name)
        # specific subscription
        client.resolve(tradesArray, messageHash)

    def handle_trades(self, client: Client, message, subscription):
        #
        # initial snapshot
        #
        #    [
        #        188687,  # channel id
        #        [
        #          [1128060675, 1654701572690, 0.00217533, 1815.3],  # id, mts, amount, price
        #          [1128060665, 1654701551231, -0.00280472, 1814.1],
        #          [1128060664, 1654701550996, -0.00364444, 1814.1],
        #          [1128060656, 1654701527730, -0.00265203, 1814.2],
        #          [1128060647, 1654701505193, 0.00262395, 1815.2],
        #          [1128060642, 1654701484656, -0.13411443, 1816],
        #          [1128060641, 1654701484656, -0.00088557, 1816],
        #          [1128060639, 1654701478326, -0.002, 1816],
        #        ]
        #    ]
        # update
        #
        #    [
        #        360141,
        #        "te",
        #        [
        #            1128060969,  # id
        #            1654702500098,  # mts
        #            0.00325131,  # amount positive buy, negative sell
        #            1818.5,  # price
        #        ],
        #    ]
        #
        #
        channel = self.safe_value(subscription, 'channel')
        marketId = self.safe_string(subscription, 'symbol')
        market = self.safe_market(marketId)
        messageHash = channel + ':' + marketId
        tradesLimit = self.safe_integer(self.options, 'tradesLimit', 1000)
        symbol = market['symbol']
        stored = self.safe_value(self.trades, symbol)
        if stored is None:
            stored = ArrayCache(tradesLimit)
            self.trades[symbol] = stored
        messageLength = len(message)
        if messageLength == 2:
            # initial snapshot
            trades = self.safe_value(message, 1, [])
            for i in range(0, len(trades)):
                parsed = self.parse_ws_trade(trades[i], market)
                stored.append(parsed)
        else:
            # update
            type = self.safe_string(message, 1)
            if type == 'tu':
                # don't resolve for a duplicate update
                # since te and tu updates are duplicated on the public stream
                return
            trade = self.safe_value(message, 2, [])
            parsed = self.parse_ws_trade(trade, market)
            stored.append(parsed)
        client.resolve(stored, messageHash)
        return message

    def parse_ws_trade(self, trade, market=None):
        #
        #    [
        #        1128060969,  # id
        #        1654702500098,  # mts
        #        0.00325131,  # amount positive buy, negative sell
        #        1818.5,  # price
        #    ]
        #
        # trade execution
        #
        #    [
        #        1133411090,  # id
        #        "tLTCUST",  # symbol
        #        1655110144598,  # create ms
        #        97084883506,  # order id
        #        0.1,  # amount
        #        42.821,  # price
        #        "EXCHANGE MARKET",  # order type
        #        42.799,  # order price
        #        -1,  # maker
        #        null,  # fee
        #        null,  # fee currency
        #        1655110144596  # cid
        #    ]
        #
        # trade update
        #
        #    [
        #       1133411090,
        #       "tLTCUST",
        #       1655110144598,
        #       97084883506,
        #       0.1,
        #       42.821,
        #       "EXCHANGE MARKET",
        #       42.799,
        #       -1,
        #       -0.0002,
        #       "LTC",
        #       1655110144596
        #    ]
        #
        numFields = len(trade)
        isPublic = numFields <= 8
        marketId = self.safe_string(trade, 1) if (not isPublic) else None
        market = self.safe_market(marketId, market)
        createdKey = 1 if isPublic else 2
        priceKey = 3 if isPublic else 5
        amountKey = 2 if isPublic else 4
        marketId = market['id']
        type = self.safe_string(trade, 6)
        if type is not None:
            if type.find('LIMIT') > -1:
                type = 'limit'
            elif type.find('MARKET') > -1:
                type = 'market'
        orderId = self.safe_string(trade, 3) if (not isPublic) else None
        id = self.safe_string(trade, 0)
        timestamp = self.safe_integer(trade, createdKey)
        price = self.safe_string(trade, priceKey)
        amountString = self.safe_string(trade, amountKey)
        amount = self.parse_number(Precise.string_abs(amountString))
        side = None
        if amount is not None:
            side = 'buy' if Precise.string_gt(amountString, '0') else 'sell'
        symbol = self.safe_symbol(marketId, market)
        feeValue = self.safe_string(trade, 9)
        fee = None
        if feeValue is not None:
            currencyId = self.safe_string(trade, 10)
            code = self.safe_currency_code(currencyId)
            fee = {
                'cost': feeValue,
                'currency': code,
            }
        maker = self.safe_integer(trade, 8)
        takerOrMaker = None
        if maker is not None:
            takerOrMaker = 'taker' if (maker == -1) else 'maker'
        return self.safe_trade({
            'info': trade,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'symbol': symbol,
            'id': id,
            'order': orderId,
            'type': type,
            'takerOrMaker': takerOrMaker,
            'side': side,
            'price': price,
            'amount': amount,
            'cost': None,
            'fee': fee,
        }, market)

    def handle_ticker(self, client: Client, message, subscription):
        #
        # [
        #    340432,  # channel ID
        #     [
        #         236.62,        # 1 BID float Price of last highest bid
        #         9.0029,        # 2 BID_SIZE float Size of the last highest bid
        #         236.88,        # 3 ASK float Price of last lowest ask
        #         7.1138,        # 4 ASK_SIZE float Size of the last lowest ask
        #         -1.02,         # 5 DAILY_CHANGE float Amount that the last price has changed since yesterday
        #         0,             # 6 DAILY_CHANGE_PERC float Amount that the price has changed expressed in percentage terms
        #         236.52,        # 7 LAST_PRICE float Price of the last trade.
        #         5191.36754297,  # 8 VOLUME float Daily volume
        #         250.01,        # 9 HIGH float Daily high
        #         220.05,        # 10 LOW float Daily low
        #     ]
        #  ]
        #
        ticker = self.safe_value(message, 1)
        marketId = self.safe_string(subscription, 'symbol')
        market = self.safe_market(marketId)
        symbol = self.safe_symbol(marketId)
        parsed = self.parse_ws_ticker(ticker, market)
        channel = 'ticker'
        messageHash = channel + ':' + marketId
        self.tickers[symbol] = parsed
        client.resolve(parsed, messageHash)

    def parse_ws_ticker(self, ticker, market=None):
        #
        #     [
        #         236.62,        # 1 BID float Price of last highest bid
        #         9.0029,        # 2 BID_SIZE float Size of the last highest bid
        #         236.88,        # 3 ASK float Price of last lowest ask
        #         7.1138,        # 4 ASK_SIZE float Size of the last lowest ask
        #         -1.02,         # 5 DAILY_CHANGE float Amount that the last price has changed since yesterday
        #         0,             # 6 DAILY_CHANGE_PERC float Amount that the price has changed expressed in percentage terms
        #         236.52,        # 7 LAST_PRICE float Price of the last trade.
        #         5191.36754297,  # 8 VOLUME float Daily volume
        #         250.01,        # 9 HIGH float Daily high
        #         220.05,        # 10 LOW float Daily low
        #     ]
        #
        market = self.safe_market(None, market)
        symbol = market['symbol']
        last = self.safe_string(ticker, 6)
        change = self.safe_string(ticker, 4)
        return self.safe_ticker({
            'symbol': symbol,
            'timestamp': None,
            'datetime': None,
            'high': self.safe_string(ticker, 8),
            'low': self.safe_string(ticker, 9),
            'bid': self.safe_string(ticker, 0),
            'bidVolume': self.safe_string(ticker, 1),
            'ask': self.safe_string(ticker, 2),
            'askVolume': self.safe_string(ticker, 3),
            'vwap': None,
            'open': None,
            'close': last,
            'last': last,
            'previousClose': None,
            'change': change,
            'percentage': self.safe_string(ticker, 5),
            'average': None,
            'baseVolume': self.safe_string(ticker, 7),
            'quoteVolume': None,
            'info': ticker,
        }, market)

    async def watch_order_book(self, symbol: str, limit: Int = None, params={}) -> OrderBook:
        """
        watches information on open orders with bid(buy) and ask(sell) prices, volumes and other data
        :param str symbol: unified symbol of the market to fetch the order book for
        :param int [limit]: the maximum amount of order book entries to return
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: A dictionary of `order book structures <https://docs.ccxt.com/#/?id=order-book-structure>` indexed by market symbols
        """
        if limit is not None:
            if (limit != 25) and (limit != 100):
                raise ExchangeError(self.id + ' watchOrderBook limit argument must be None, 25 or 100')
        options = self.safe_value(self.options, 'watchOrderBook', {})
        prec = self.safe_string(options, 'prec', 'P0')
        freq = self.safe_string(options, 'freq', 'F0')
        request = {
            'prec': prec,  # string, level of price aggregation, 'P0', 'P1', 'P2', 'P3', 'P4', default P0
            'freq': freq,  # string, frequency of updates 'F0' = realtime, 'F1' = 2 seconds, default is 'F0'
        }
        if limit is not None:
            request['len'] = limit  # string, number of price points, '25', '100', default = '25'
        orderbook = await self.subscribe('book', symbol, self.deep_extend(request, params))
        return orderbook.limit()

    def handle_order_book(self, client: Client, message, subscription):
        #
        # first message(snapshot)
        #
        #     [
        #         18691,  # channel id
        #         [
        #             [7364.8, 10, 4.354802],  # price, count, size > 0 = bid
        #             [7364.7, 1, 0.00288831],
        #             [7364.3, 12, 0.048],
        #             [7364.9, 3, -0.42028976],  # price, count, size < 0 = ask
        #             [7365, 1, -0.25],
        #             [7365.5, 1, -0.00371937],
        #         ]
        #     ]
        #
        # subsequent updates
        #
        #     [
        #         358169,  # channel id
        #         [
        #            1807.1,  # price
        #            0,  # cound
        #            1  # size
        #         ]
        #     ]
        #
        marketId = self.safe_string(subscription, 'symbol')
        symbol = self.safe_symbol(marketId)
        channel = 'book'
        messageHash = channel + ':' + marketId
        prec = self.safe_string(subscription, 'prec', 'P0')
        isRaw = (prec == 'R0')
        # if it is an initial snapshot
        orderbook = self.safe_value(self.orderbooks, symbol)
        if orderbook is None:
            limit = self.safe_integer(subscription, 'len')
            if isRaw:
                # raw order books
                self.orderbooks[symbol] = self.indexed_order_book({}, limit)
            else:
                # P0, P1, P2, P3, P4
                self.orderbooks[symbol] = self.counted_order_book({}, limit)
            orderbook = self.orderbooks[symbol]
            if isRaw:
                deltas = message[1]
                for i in range(0, len(deltas)):
                    delta = deltas[i]
                    size = -delta[2] if (delta[2] < 0) else delta[2]
                    side = 'asks' if (delta[2] < 0) else 'bids'
                    bookside = orderbook[side]
                    idString = self.safe_string(delta, 0)
                    price = self.safe_float(delta, 1)
                    bookside.store(price, size, idString)
            else:
                deltas = message[1]
                for i in range(0, len(deltas)):
                    delta = deltas[i]
                    amount = self.safe_number(delta, 2)
                    counter = self.safe_number(delta, 1)
                    price = self.safe_number(delta, 0)
                    size = -amount if (amount < 0) else amount
                    side = 'asks' if (amount < 0) else 'bids'
                    bookside = orderbook[side]
                    bookside.store(price, size, counter)
            orderbook['symbol'] = symbol
            client.resolve(orderbook, messageHash)
        else:
            deltas = message[1]
            orderbookItem = self.orderbooks[symbol]
            if isRaw:
                price = self.safe_string(deltas, 1)
                size = -deltas[2] if (deltas[2] < 0) else deltas[2]
                side = 'asks' if (deltas[2] < 0) else 'bids'
                bookside = orderbookItem[side]
                # price = 0 means that you have to remove the order from your book
                amount = size if Precise.string_gt(price, '0') else '0'
                idString = self.safe_string(deltas, 0)
                bookside.store(self.parse_number(price), self.parse_number(amount), idString)
            else:
                amount = self.safe_string(deltas, 2)
                counter = self.safe_string(deltas, 1)
                price = self.safe_string(deltas, 0)
                size = Precise.string_neg(amount) if Precise.string_lt(amount, '0') else amount
                side = 'asks' if Precise.string_lt(amount, '0') else 'bids'
                bookside = orderbookItem[side]
                bookside.store(self.parse_number(price), self.parse_number(size), self.parse_number(counter))
            client.resolve(orderbook, messageHash)

    def handle_checksum(self, client: Client, message, subscription):
        #
        # [173904, "cs", -890884919]
        #
        marketId = self.safe_string(subscription, 'symbol')
        symbol = self.safe_symbol(marketId)
        channel = 'book'
        messageHash = channel + ':' + marketId
        book = self.safe_value(self.orderbooks, symbol)
        if book is None:
            return
        depth = 25  # covers the first 25 bids and asks
        stringArray = []
        bids = book['bids']
        asks = book['asks']
        prec = self.safe_string(subscription, 'prec', 'P0')
        isRaw = (prec == 'R0')
        idToCheck = 2 if isRaw else 0
        # pepperoni pizza from bitfinex
        for i in range(0, depth):
            bid = self.safe_value(bids, i)
            ask = self.safe_value(asks, i)
            if bid is not None:
                stringArray.append(self.number_to_string(bids[i][idToCheck]))
                stringArray.append(self.number_to_string(bids[i][1]))
            if ask is not None:
                stringArray.append(self.number_to_string(asks[i][idToCheck]))
                stringArray.append(self.number_to_string(-asks[i][1]))
        payload = ':'.join(stringArray)
        localChecksum = self.crc32(payload, True)
        responseChecksum = self.safe_integer(message, 2)
        if responseChecksum != localChecksum:
            error = InvalidNonce(self.id + ' invalid checksum')
            client.reject(error, messageHash)

    async def watch_balance(self, params={}) -> Balances:
        """
        watch balance and get the amount of funds available for trading or funds locked in orders
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.type]: spot or contract if not provided self.options['defaultType'] is used
        :returns dict: a `balance structure <https://docs.ccxt.com/#/?id=balance-structure>`
        """
        await self.load_markets()
        balanceType = self.safe_string(params, 'wallet', 'exchange')  # exchange, margin
        params = self.omit(params, 'wallet')
        messageHash = 'balance:' + balanceType
        return await self.subscribe_private(messageHash)

    def handle_balance(self, client: Client, message, subscription):
        #
        # snapshot(exchange + margin together)
        #   [
        #       0,
        #       "ws",
        #       [
        #           [
        #               "exchange",
        #               "LTC",
        #               0.********,
        #               0,
        #               null,
        #               "Trading fees for 0.05 LTC(LTCUST) @ 51.872 on BFX(0.2%)",
        #               null,
        #           ]
        #           [
        #               "margin",
        #               "USTF0",
        #               11.960650700086292,
        #               0,
        #               null,
        #               "Trading fees for 0.1 LTCF0(LTCF0:USTF0) @ 51.844 on BFX(0.065%)",
        #               null,
        #           ],
        #       ],
        #   ]
        #
        # spot
        #   [
        #       0,
        #       "wu",
        #       [
        #         "exchange",
        #         "LTC",  # currency
        #         0.06729727,  # wallet balance
        #         0,  # unsettled balance
        #         0.06729727,  # available balance might be null
        #         "Exchange 0.4 LTC for UST @ 65.075",
        #         {
        #           "reason": "TRADE",
        #           "order_id": 96596397973,
        #           "order_id_oppo": 96596632735,
        #           "trade_price": "65.075",
        #           "trade_amount": "-0.4",
        #           "order_cid": 1654636218766,
        #           "order_gid": null
        #         }
        #       ]
        #   ]
        #
        # margin
        #
        #   [
        #       "margin",
        #       "USTF0",
        #       11.960650700086292,  # total
        #       0,
        #       6.776250700086292,  # available
        #       "Trading fees for 0.1 LTCF0(LTCF0:USTF0) @ 51.844 on BFX(0.065%)",
        #       null
        #   ]
        #
        updateType = self.safe_value(message, 1)
        data = None
        if updateType == 'ws':
            data = self.safe_value(message, 2)
        else:
            data = [self.safe_value(message, 2)]
        updatedTypes = {}
        for i in range(0, len(data)):
            rawBalance = data[i]
            currencyId = self.safe_string(rawBalance, 1)
            code = self.safe_currency_code(currencyId)
            balance = self.parse_ws_balance(rawBalance)
            balanceType = self.safe_string(rawBalance, 0)
            oldBalance = self.safe_value(self.balance, balanceType, {})
            oldBalance[code] = balance
            oldBalance['info'] = message
            self.balance[balanceType] = self.safe_balance(oldBalance)
            updatedTypes[balanceType] = True
        updatesKeys = list(updatedTypes.keys())
        for i in range(0, len(updatesKeys)):
            type = updatesKeys[i]
            messageHash = 'balance:' + type
            client.resolve(self.balance[type], messageHash)

    def parse_ws_balance(self, balance):
        #
        #     [
        #         "exchange",
        #         "LTC",
        #         0.********,  # balance
        #         0,
        #         null,  # available null if not calculated yet
        #         "Trading fees for 0.05 LTC(LTCUST) @ 51.872 on BFX(0.2%)",
        #         null,
        #     ]
        #
        totalBalance = self.safe_string(balance, 2)
        availableBalance = self.safe_string(balance, 4)
        account = self.account()
        if availableBalance is not None:
            account['free'] = availableBalance
        account['total'] = totalBalance
        return account

    def handle_system_status(self, client: Client, message):
        #
        #     {
        #         "event": "info",
        #         "version": 2,
        #         "serverId": "e293377e-7bb7-427e-b28c-5db045b2c1d1",
        #         "platform": {status: 1},  # 1 for operative, 0 for maintenance
        #     }
        #
        return message

    def handle_subscription_status(self, client: Client, message):
        #
        #     {
        #         "event": "subscribed",
        #         "channel": "book",
        #         "chanId": 67473,
        #         "symbol": "tBTCUSD",
        #         "prec": "P0",
        #         "freq": "F0",
        #         "len": "25",
        #         "pair": "BTCUSD"
        #     }
        #
        channelId = self.safe_string(message, 'chanId')
        client.subscriptions[channelId] = message
        return message

    async def authenticate(self, params={}):
        url = self.urls['api']['ws']['private']
        client = self.client(url)
        messageHash = 'authenticated'
        future = client.future(messageHash)
        authenticated = self.safe_value(client.subscriptions, messageHash)
        if authenticated is None:
            nonce = self.milliseconds()
            payload = 'AUTH' + str(nonce)
            signature = self.hmac(self.encode(payload), self.encode(self.secret), hashlib.sha384, 'hex')
            event = 'auth'
            request = {
                'apiKey': self.apiKey,
                'authSig': signature,
                'authNonce': nonce,
                'authPayload': payload,
                'event': event,
            }
            message = self.extend(request, params)
            self.watch(url, messageHash, message, messageHash)
        return future

    def handle_authentication_message(self, client: Client, message):
        messageHash = 'authenticated'
        status = self.safe_string(message, 'status')
        if status == 'OK':
            # we resolve the future here permanently so authentication only happens once
            future = self.safe_value(client.futures, messageHash)
            future.resolve(True)
        else:
            error = AuthenticationError(self.json(message))
            client.reject(error, messageHash)
            # allows further authentication attempts
            if messageHash in client.subscriptions:
                del client.subscriptions[messageHash]

    async def watch_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        watches information on multiple orders made by the user
        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure
        """
        await self.load_markets()
        messageHash = 'orders'
        if symbol is not None:
            market = self.market(symbol)
            messageHash += ':' + market['id']
        orders = await self.subscribe_private(messageHash)
        if self.newUpdates:
            limit = orders.getLimit(symbol, limit)
        return self.filter_by_symbol_since_limit(orders, symbol, since, limit, True)

    def handle_orders(self, client: Client, message, subscription):
        #
        # limit order
        #    [
        #        0,
        #        "on",  # ou or oc
        #        [
        #           96923856256,  # order id
        #           null,  # gid
        #           1655029337026,  # cid
        #           "tLTCUST",  # symbol
        #           1655029337027,  # created timestamp
        #           1655029337029,  # updated timestamp
        #           0.1,  # amount
        #           0.1,  # amount_orig
        #           "EXCHANGE LIMIT",  # order type
        #           null,  # type_prev
        #           null,  # mts_tif
        #           null,  # placeholder
        #           0,  # flags
        #           "ACTIVE",  # status
        #           null,
        #           null,
        #           30,  # price
        #           0,  # price average
        #           0,  # price_trailling
        #           0,  # price_aux_limit
        #           null,
        #           null,
        #           null,
        #           0,  # notify
        #           0,
        #           null,
        #           null,
        #           null,
        #           "BFX",
        #           null,
        #           null,
        #        ]
        #    ]
        #
        data = self.safe_value(message, 2, [])
        messageType = self.safe_string(message, 1)
        if self.orders is None:
            limit = self.safe_integer(self.options, 'ordersLimit', 1000)
            self.orders = ArrayCacheBySymbolById(limit)
        orders = self.orders
        symbolIds = {}
        if messageType == 'os':
            snapshotLength = len(data)
            if snapshotLength == 0:
                return
            for i in range(0, len(data)):
                value = data[i]
                parsed = self.parse_ws_order(value)
                symbol = parsed['symbol']
                symbolIds[symbol] = True
                orders.append(parsed)
        else:
            parsed = self.parse_ws_order(data)
            orders.append(parsed)
            symbol = parsed['symbol']
            symbolIds[symbol] = True
        name = 'orders'
        client.resolve(self.orders, name)
        keys = list(symbolIds.keys())
        for i in range(0, len(keys)):
            symbol = keys[i]
            market = self.market(symbol)
            messageHash = name + ':' + market['id']
            client.resolve(self.orders, messageHash)

    def parse_ws_order_status(self, status):
        statuses = {
            'ACTIVE': 'open',
            'CANCELED': 'canceled',
            'EXECUTED': 'closed',
            'PARTIALLY': 'open',
        }
        return self.safe_string(statuses, status, status)

    def parse_ws_order(self, order, market=None):
        #
        #   [
        #       97084883506,  # order id
        #       null,
        #       1655110144596,  # clientOrderId
        #       "tLTCUST",  # symbol
        #       1655110144596,  # created timestamp
        #       1655110144598,  # updated timestamp
        #       0,  # amount
        #       0.1,  # amount_orig negative if sell order
        #       "EXCHANGE MARKET",  # type
        #       null,
        #       null,
        #       null,
        #       0,
        #       "EXECUTED @ 42.821(0.1)",  # status
        #       null,
        #       null,
        #       42.799,  # price
        #       42.821,  # price average
        #       0,  # price trailling
        #       0,  # price_aux_limit
        #       null,
        #       null,
        #       null,
        #       0,
        #       0,
        #       null,
        #       null,
        #       null,
        #       "BFX",
        #       null,
        #       null,
        #       {}
        #   ]
        #
        id = self.safe_string(order, 0)
        clientOrderId = self.safe_string(order, 1)
        marketId = self.safe_string(order, 3)
        symbol = self.safe_symbol(marketId)
        market = self.safe_market(symbol)
        amount = self.safe_string(order, 7)
        side = 'buy'
        if Precise.string_lt(amount, '0'):
            amount = Precise.string_abs(amount)
            side = 'sell'
        remaining = Precise.string_abs(self.safe_string(order, 6))
        type = self.safe_string(order, 8)
        if type.find('LIMIT') > -1:
            type = 'limit'
        elif type.find('MARKET') > -1:
            type = 'market'
        rawState = self.safe_string(order, 13)
        stateParts = rawState.split(' ')
        trimmedStatus = self.safe_string(stateParts, 0)
        status = self.parse_ws_order_status(trimmedStatus)
        price = self.safe_string(order, 16)
        timestamp = self.safe_integer_2(order, 5, 4)
        average = self.safe_string(order, 17)
        stopPrice = self.omit_zero(self.safe_string(order, 18))
        return self.safe_order({
            'info': order,
            'id': id,
            'clientOrderId': clientOrderId,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'lastTradeTimestamp': None,
            'symbol': symbol,
            'type': type,
            'side': side,
            'price': price,
            'stopPrice': stopPrice,
            'triggerPrice': stopPrice,
            'average': average,
            'amount': amount,
            'remaining': remaining,
            'filled': None,
            'status': status,
            'fee': None,
            'cost': None,
            'trades': None,
        }, market)

    def handle_message(self, client: Client, message):
        channelId = self.safe_string(message, 0)
        #
        #     [
        #         1231,
        #         "hb",
        #     ]
        #
        # auth message
        #    {
        #        "event": "auth",
        #        "status": "OK",
        #        "chanId": 0,
        #        "userId": 3159883,
        #        "auth_id": "ac7108e7-2f26-424d-9982-c24700dc02ca",
        #        "caps": {
        #          "orders": {read: 1, write: 1},
        #          "account": {read: 1, write: 1},
        #          "funding": {read: 1, write: 1},
        #          "history": {read: 1, write: 0},
        #          "wallets": {read: 1, write: 1},
        #          "withdraw": {read: 0, write: 1},
        #          "positions": {read: 1, write: 1},
        #          "ui_withdraw": {read: 0, write: 0}
        #        }
        #    }
        #
        if isinstance(message, list):
            if message[1] == 'hb':
                return message  # skip heartbeats within subscription channels for now
            subscription = self.safe_value(client.subscriptions, channelId, {})
            channel = self.safe_string(subscription, 'channel')
            name = self.safe_string(message, 1)
            publicMethods = {
                'book': self.handle_order_book,
                'cs': self.handle_checksum,
                'candles': self.handle_ohlcv,
                'ticker': self.handle_ticker,
                'trades': self.handle_trades,
            }
            privateMethods = {
                'os': self.handle_orders,
                'ou': self.handle_orders,
                'on': self.handle_orders,
                'oc': self.handle_orders,
                'wu': self.handle_balance,
                'ws': self.handle_balance,
                'tu': self.handle_my_trade,
            }
            method = None
            if channelId == '0':
                method = self.safe_value(privateMethods, name)
            else:
                method = self.safe_value_2(publicMethods, name, channel)
            if method is None:
                return message
            else:
                return method(client, message, subscription)
        else:
            event = self.safe_string(message, 'event')
            if event is not None:
                methods = {
                    'info': self.handle_system_status,
                    'subscribed': self.handle_subscription_status,
                    'auth': self.handle_authentication_message,
                }
                method = self.safe_value(methods, event)
                if method is None:
                    return message
                else:
                    return method(client, message)
