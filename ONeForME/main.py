import asyncio
from telegram import Update
from telegram.ext import ApplicationBuilder, ContextTypes
from bot.handlers import (
    start_command,
    status_command,
    help_command,
    unknown_command,
    error_handler
)
from core.exchange_manager import ExchangeManager
from core.strategy_manager import StrategyManager
from core.risk_manager import RiskManager
from config.settings import settings
from loguru import logger

async def main():
    # Initialiseer managers
    exchange_manager = ExchangeManager()
    strategy_manager = StrategyManager(exchange_manager)
    risk_manager = RiskManager(exchange_manager)
    
    # Initialiseer Telegram-bot
    application = ApplicationBuilder().token(settings.TELEGRAM_BOT_TOKEN).build()
    
    # Registreer commando's
    application.add_handler(start_command)
    application.add_handler(status_command)
    application.add_handler(help_command)
    application.add_handler(unknown_command)
    application.add_error_handler(error_handler)
    
    # Start de bot
    await application.initialize()
    logger.info("Telegram-bot is gestart")
    
    # Wacht op signalen
    await application.start()
    
    # Houdt de bot draaiende
    await application.idle()
    
    # Netjes afsluiten
    await application.stop()
    await exchange_manager.close_exchanges()
    logger.info("Telegram-bot en exchanges zijn gestopt")

if __name__ == "__main__":
    asyncio.run(main())
