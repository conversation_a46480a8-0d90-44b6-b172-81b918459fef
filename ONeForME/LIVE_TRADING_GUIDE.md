# 🚨 LIVE TRADING GIDS - <PERSON>BRUIK ECHT GELD

⚠️ **WAARSCHUWING: Live trading betekent dat je echt geld kunt verliezen!**

## 📋 Checklist voordat je start

### 1. **Veiligheidscheck uitvoeren**
```bash
python safety_check.py
```
✅ Alle checks moeten slagen voordat je verder gaat!

### 2. **API Keys instellen**
Bewerk je `.env` file:

```bash
# Voor Binance
BINANCE_API_KEY=je_echte_api_key
BINANCE_SECRET=je_echte_secret

# Voor KuCoin  
KUCOIN_API_KEY=je_echte_api_key
KUCOIN_SECRET=je_echte_secret
KUCOIN_PASSWORD=je_echte_password
```

⚠️ **Zorg ervoor dat je API keys ALLEEN trading rechten hebben, GEEN withdrawal rechten!**

### 3. **Admin gebruikers instellen**
```bash
# In .env - je eigen Telegram user ID
ADMIN_USER_IDS=jouw_telegram_user_id
```

### 4. **Veilige instellingen configureren**
```bash
# In .env - CONSERVATIEVE instellingen voor beginners
RISK_PERCENT_PER_TRADE=0.5          # Start met 0.5%!
MAX_CONCURRENT_POSITIONS=1          # Start met 1 positie
MAX_DRAWDOWN_PERCENT=5.0            # Max 5% verlies
PAPER_TRADING=false                 # Zet paper trading uit
LIVE_TRADING_ENABLED=true           # Expliciet live trading aan
EMERGENCY_STOP=false                # Zorg dat emergency stop uit staat
```

## 🛡️ Veiligheidsstappen

### Stap 1: Test met kleine bedragen
- Start met minimale bedragen (€10-50)
- Test alle functionaliteit
- Controleer of orders correct worden geplaatst

### Stap 2: Monitoring instellen
```bash
# In .env
MONITORING_CHANNEL=je_telegram_channel_id
```

### Stap 3: Emergency procedures
- Zorg dat je altijd toegang hebt tot `/emergency` commando
- Houd je exchange app bij de hand voor handmatige controle
- Stel alerts in voor grote verliezen

## 🚀 Live trading starten

### 1. **Laatste veiligheidscheck**
```bash
python safety_check.py
```

### 2. **Bot starten**
```bash
python main.py
```

### 3. **Telegram commando's testen**
- `/start` - Controleer of bot reageert
- `/status` - Controleer manager status
- `/safety` - Voer veiligheidscheck uit via Telegram

## 📱 Belangrijke Telegram commando's

- `/start` - Start bot interface
- `/status` - Toon bot en manager status
- `/safety` - Voer veiligheidscheck uit (alleen admins)
- `/emergency` - STOP alle trading onmiddellijk (alleen admins)
- `/help` - Toon alle beschikbare commando's

## ⚠️ Risico's en waarschuwingen

### **Financiële risico's:**
- Je kunt al je geld verliezen
- Crypto markten zijn zeer volatiel
- Technische problemen kunnen leiden tot verliezen

### **Technische risico's:**
- Internet verbinding kan wegvallen
- Exchange API kan offline gaan
- Bot kan bugs hebben

### **Operationele risico's:**
- Verkeerde configuratie
- Menselijke fouten
- Markt manipulatie

## 🛑 Emergency procedures

### Als er iets mis gaat:

1. **Via Telegram:**
   ```
   /emergency
   ```

2. **Handmatig op exchange:**
   - Log in op je exchange
   - Sluit alle open posities
   - Annuleer alle open orders

3. **Bot stoppen:**
   ```bash
   # In terminal waar bot draait
   Ctrl+C
   ```

## 📊 Monitoring en logging

### Logs controleren:
```bash
tail -f logs/trading_bot.log
```

### Status controleren:
- Gebruik `/status` commando regelmatig
- Controleer account balance op exchange
- Monitor open posities

## 🎯 Aanbevolen startinstellingen

Voor **beginners** (eerste keer live trading):
```bash
RISK_PERCENT_PER_TRADE=0.5
MAX_CONCURRENT_POSITIONS=1
MAX_DRAWDOWN_PERCENT=3.0
MAX_DAILY_TRADES=5
```

Voor **ervaren** traders:
```bash
RISK_PERCENT_PER_TRADE=1.0
MAX_CONCURRENT_POSITIONS=2
MAX_DRAWDOWN_PERCENT=5.0
MAX_DAILY_TRADES=10
```

Voor **professionals** (op eigen risico):
```bash
RISK_PERCENT_PER_TRADE=2.0
MAX_CONCURRENT_POSITIONS=3
MAX_DRAWDOWN_PERCENT=10.0
MAX_DAILY_TRADES=20
```

## 🔒 Beveiliging

### API Key beveiliging:
- Gebruik ALLEEN trading rechten
- GEEN withdrawal rechten
- Gebruik IP whitelisting indien mogelijk
- Roteer keys regelmatig

### Bot beveiliging:
- Alleen jij moet admin zijn
- Gebruik sterke wachtwoorden
- Houd software up-to-date

## 📞 Support

Als je problemen hebt:
1. Check de logs: `tail -f logs/trading_bot.log`
2. Voer veiligheidscheck uit: `python safety_check.py`
3. Stop trading indien nodig: `/emergency`

---

**🚨 LAATSTE WAARSCHUWING: Trading is risicovol. Investeer alleen wat je kunt missen!**
